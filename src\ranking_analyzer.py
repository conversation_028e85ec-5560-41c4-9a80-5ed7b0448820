"""
排名统计分析模块
负责板块排名频次和单日冠军统计
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
import os
import sys
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

# 忽略pandas的警告
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

class RankingAnalyzer:
    """排名分析器类"""

    def __init__(self, data: pd.DataFrame):
        """
        初始化排名分析器

        Args:
            data: 多级索引的DataFrame，索引为(date, sector_code)
        """
        self.data = data
        self.logger = self._setup_logger()
        self.daily_rankings_cache = None

        # 验证数据格式
        if not isinstance(data.index, pd.MultiIndex):
            raise ValueError("数据必须具有多级索引 (date, sector_code)")

        if 'date' not in data.index.names or 'sector_code' not in data.index.names:
            raise ValueError("索引必须包含 'date' 和 'sector_code'")

        if 'change_pct' not in data.columns:
            raise ValueError("数据必须包含 'change_pct' 列")

        # 获取基本信息
        self.available_dates = sorted(data.index.get_level_values('date').unique())
        self.all_sectors = sorted(data.index.get_level_values('sector_code').unique())

        self.logger.info(f"排名分析器初始化完成")
        self.logger.info(f"数据日期范围: {self.available_dates[0]} 到 {self.available_dates[-1]}")
        self.logger.info(f"总交易日数: {len(self.available_dates)}")
        self.logger.info(f"板块总数: {len(self.all_sectors)}")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def calculate_daily_rankings(self, use_cache: bool = True) -> pd.DataFrame:
        """
        计算每日板块涨跌幅排名

        Args:
            use_cache: 是否使用缓存

        Returns:
            包含每日排名的DataFrame
        """
        if use_cache and self.daily_rankings_cache is not None:
            self.logger.debug("使用缓存的每日排名数据")
            return self.daily_rankings_cache

        self.logger.info("开始计算每日板块排名...")

        ranking_results = []

        for date in self.available_dates:
            try:
                # 获取当日所有板块数据
                daily_data = self.data.loc[date]

                if daily_data.empty:
                    self.logger.warning(f"日期 {date} 没有数据")
                    continue

                # 按涨跌幅排序（降序）
                daily_sorted = daily_data.sort_values('change_pct', ascending=False)

                # 处理并列排名的情况
                daily_sorted['rank'] = daily_sorted['change_pct'].rank(method='min', ascending=False)

                # 为每个板块添加日期和排名信息
                for sector_code, row in daily_sorted.iterrows():
                    sector_name = row['sector_name'] if 'sector_name' in row else sector_code

                    ranking_results.append({
                        'date': date,
                        'sector_code': sector_code,
                        'sector_name': sector_name,
                        'change_pct': row['change_pct'],
                        'rank': int(row['rank']),
                        'is_champion': row['rank'] == 1,  # 是否为当日冠军
                        'is_top10': row['rank'] <= 10,    # 是否进入前10
                        'is_top5': row['rank'] <= 5,      # 是否进入前5
                        'is_top3': row['rank'] <= 3       # 是否进入前3
                    })

            except Exception as e:
                self.logger.warning(f"计算日期 {date} 排名时出错: {str(e)}")
                continue

        if not ranking_results:
            self.logger.error("没有成功计算任何日期的排名")
            return pd.DataFrame()

        # 转换为DataFrame
        rankings_df = pd.DataFrame(ranking_results)

        # 设置多级索引
        rankings_df = rankings_df.set_index(['date', 'sector_code'])

        # 缓存结果
        self.daily_rankings_cache = rankings_df

        self.logger.info(f"每日排名计算完成，共 {len(rankings_df)} 条记录")
        return rankings_df

    def count_daily_champions(self, window_days: Optional[int] = None,
                             start_date: Optional[Union[str, pd.Timestamp]] = None,
                             end_date: Optional[Union[str, pd.Timestamp]] = None) -> pd.DataFrame:
        """
        统计指定时间窗口内单日冠军次数

        Args:
            window_days: 时间窗口天数（从最新日期回退）
            start_date: 开始日期（与window_days互斥）
            end_date: 结束日期（与window_days互斥）

        Returns:
            单日冠军统计DataFrame
        """
        self.logger.info("开始统计单日冠军板块...")

        # 获取每日排名数据
        daily_rankings = self.calculate_daily_rankings()

        if daily_rankings.empty:
            return pd.DataFrame()

        # 确定分析的时间范围
        if window_days is not None:
            # 使用时间窗口
            end_date_actual = self.available_dates[-1]
            start_idx = max(0, len(self.available_dates) - window_days)
            start_date_actual = self.available_dates[start_idx]
            analysis_dates = self.available_dates[start_idx:]

            self.logger.info(f"分析时间窗口: 最近 {window_days} 个交易日 ({start_date_actual} 到 {end_date_actual})")

        elif start_date is not None and end_date is not None:
            # 使用指定日期范围
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)

            analysis_dates = [d for d in self.available_dates if start_date <= d <= end_date]

            if not analysis_dates:
                self.logger.warning(f"指定日期范围内没有交易日: {start_date} 到 {end_date}")
                return pd.DataFrame()

            self.logger.info(f"分析日期范围: {analysis_dates[0]} 到 {analysis_dates[-1]} (共{len(analysis_dates)}个交易日)")

        else:
            # 使用全部数据
            analysis_dates = self.available_dates
            self.logger.info(f"分析全部数据: {len(analysis_dates)} 个交易日")

        # 筛选指定时间范围的数据
        filtered_rankings = daily_rankings[daily_rankings.index.get_level_values('date').isin(analysis_dates)]

        # 筛选出冠军记录
        champions = filtered_rankings[filtered_rankings['is_champion'] == True].copy()

        if champions.empty:
            self.logger.warning("指定时间范围内没有找到冠军记录")
            return pd.DataFrame()

        # 按板块统计冠军次数
        champion_stats = []

        for sector_code in self.all_sectors:
            sector_champions = champions[champions.index.get_level_values('sector_code') == sector_code]

            if not sector_champions.empty:
                champion_count = len(sector_champions)
                sector_name = sector_champions['sector_name'].iloc[0]

                # 计算冠军日期列表
                champion_dates = sector_champions.index.get_level_values('date').tolist()

                # 计算平均冠军涨跌幅
                avg_champion_change = sector_champions['change_pct'].mean()
                max_champion_change = sector_champions['change_pct'].max()
                min_champion_change = sector_champions['change_pct'].min()

                # 计算冠军频率（冠军次数/分析期间交易日数）
                champion_frequency = champion_count / len(analysis_dates) * 100

                champion_stats.append({
                    'sector_code': sector_code,
                    'sector_name': sector_name,
                    'champion_count': champion_count,
                    'champion_frequency_pct': champion_frequency,
                    'avg_champion_change_pct': avg_champion_change,
                    'max_champion_change_pct': max_champion_change,
                    'min_champion_change_pct': min_champion_change,
                    'champion_dates': champion_dates,
                    'latest_champion_date': max(champion_dates) if champion_dates else None,
                    'analysis_days': len(analysis_dates)  # 添加分析天数信息
                })

        if not champion_stats:
            self.logger.warning("没有统计到任何冠军数据")
            return pd.DataFrame()

        # 转换为DataFrame并排序
        champions_df = pd.DataFrame(champion_stats)
        champions_df = champions_df.sort_values('champion_count', ascending=False).reset_index(drop=True)

        # 添加排名
        champions_df['rank'] = range(1, len(champions_df) + 1)

        self.logger.info(f"单日冠军统计完成，共 {len(champions_df)} 个板块获得过冠军")
        return champions_df

    def count_top10_frequency(self, window_days: Optional[int] = None,
                             start_date: Optional[Union[str, pd.Timestamp]] = None,
                             end_date: Optional[Union[str, pd.Timestamp]] = None) -> pd.DataFrame:
        """
        统计指定时间窗口内前10频次

        Args:
            window_days: 时间窗口天数（从最新日期回退）
            start_date: 开始日期（与window_days互斥）
            end_date: 结束日期（与window_days互斥）

        Returns:
            前10频次统计DataFrame
        """
        self.logger.info("开始统计前10频次...")

        # 获取每日排名数据
        daily_rankings = self.calculate_daily_rankings()

        if daily_rankings.empty:
            return pd.DataFrame()

        # 确定分析的时间范围
        if window_days is not None:
            # 使用时间窗口
            end_date_actual = self.available_dates[-1]
            start_idx = max(0, len(self.available_dates) - window_days)
            start_date_actual = self.available_dates[start_idx]
            analysis_dates = self.available_dates[start_idx:]

            self.logger.info(f"分析时间窗口: 最近 {window_days} 个交易日 ({start_date_actual} 到 {end_date_actual})")

        elif start_date is not None and end_date is not None:
            # 使用指定日期范围
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)

            analysis_dates = [d for d in self.available_dates if start_date <= d <= end_date]

            if not analysis_dates:
                self.logger.warning(f"指定日期范围内没有交易日: {start_date} 到 {end_date}")
                return pd.DataFrame()

            self.logger.info(f"分析日期范围: {analysis_dates[0]} 到 {analysis_dates[-1]} (共{len(analysis_dates)}个交易日)")

        else:
            # 使用全部数据
            analysis_dates = self.available_dates
            self.logger.info(f"分析全部数据: {len(analysis_dates)} 个交易日")

        # 筛选指定时间范围的数据
        filtered_rankings = daily_rankings[daily_rankings.index.get_level_values('date').isin(analysis_dates)]

        # 筛选前10记录
        top10_records = filtered_rankings[filtered_rankings['is_top10'] == True]

        if top10_records.empty:
            self.logger.warning("指定时间范围内没有前10记录")
            return pd.DataFrame()

        # 按板块统计前10频次
        top10_stats = []

        for sector_code in self.all_sectors:
            sector_top10 = top10_records[top10_records.index.get_level_values('sector_code') == sector_code]

            if not sector_top10.empty:
                top10_count = len(sector_top10)
                sector_name = sector_top10['sector_name'].iloc[0]

                # 计算各种统计指标
                top5_count = len(sector_top10[sector_top10['is_top5'] == True])
                top3_count = len(sector_top10[sector_top10['is_top3'] == True])
                champion_count = len(sector_top10[sector_top10['is_champion'] == True])

                # 计算频率
                top10_frequency = top10_count / len(analysis_dates) * 100
                top5_frequency = top5_count / len(analysis_dates) * 100
                top3_frequency = top3_count / len(analysis_dates) * 100
                champion_frequency = champion_count / len(analysis_dates) * 100

                # 计算平均排名和涨跌幅
                avg_rank = sector_top10['rank'].mean()
                avg_change_pct = sector_top10['change_pct'].mean()
                max_change_pct = sector_top10['change_pct'].max()
                min_change_pct = sector_top10['change_pct'].min()

                # 最佳排名
                best_rank = sector_top10['rank'].min()

                top10_stats.append({
                    'sector_code': sector_code,
                    'sector_name': sector_name,
                    'top10_count': top10_count,
                    'top5_count': top5_count,
                    'top3_count': top3_count,
                    'champion_count': champion_count,
                    'top10_frequency_pct': top10_frequency,
                    'top5_frequency_pct': top5_frequency,
                    'top3_frequency_pct': top3_frequency,
                    'champion_frequency_pct': champion_frequency,
                    'avg_rank_in_top10': avg_rank,
                    'best_rank': best_rank,
                    'avg_change_pct': avg_change_pct,
                    'max_change_pct': max_change_pct,
                    'min_change_pct': min_change_pct,
                    'analysis_days': len(analysis_dates)
                })

        if not top10_stats:
            self.logger.warning("没有板块在指定时间范围内进入过前10")
            return pd.DataFrame()

        # 转换为DataFrame并排序
        top10_df = pd.DataFrame(top10_stats)
        top10_df = top10_df.sort_values('top10_count', ascending=False).reset_index(drop=True)

        # 添加排名
        top10_df['rank'] = range(1, len(top10_df) + 1)

        self.logger.info(f"前10频次统计完成，共 {len(top10_df)} 个板块进入过前10")
        return top10_df

    def generate_ranking_report(self, window_days: Optional[int] = None) -> Dict:
        """
        生成排名统计报告

        Args:
            window_days: 分析时间窗口，None表示全部数据

        Returns:
            包含完整排名统计的字典
        """
        self.logger.info("开始生成排名统计报告...")

        try:
            # 1. 获取每日排名数据
            daily_rankings = self.calculate_daily_rankings()

            # 2. 统计单日冠军（使用相同的时间窗口）
            champions = self.count_daily_champions(window_days=window_days)

            # 3. 统计前10频次
            top10_frequency = self.count_top10_frequency(window_days=window_days)

            # 4. 生成综合报告
            report = {
                'analysis_info': {
                    'total_trading_days': len(self.available_dates),
                    'analysis_window_days': window_days or len(self.available_dates),
                    'date_range': {
                        'start': self.available_dates[0],
                        'end': self.available_dates[-1]
                    },
                    'total_sectors': len(self.all_sectors),
                    'report_generated_at': datetime.now()
                },

                'daily_champions': {
                    'summary': {
                        'total_champions': len(champions) if not champions.empty else 0,
                        'most_champion_sector': champions.iloc[0]['sector_name'] if not champions.empty else None,
                        'most_champion_count': champions.iloc[0]['champion_count'] if not champions.empty else 0,
                        'avg_champions_per_sector': champions['champion_count'].mean() if not champions.empty else 0
                    },
                    'top_10_champions': champions.head(10).to_dict('records') if not champions.empty else []
                },

                'top10_frequency': {
                    'summary': {
                        'sectors_in_top10': len(top10_frequency) if not top10_frequency.empty else 0,
                        'most_frequent_sector': top10_frequency.iloc[0]['sector_name'] if not top10_frequency.empty else None,
                        'most_frequent_count': top10_frequency.iloc[0]['top10_count'] if not top10_frequency.empty else 0,
                        'avg_top10_per_sector': top10_frequency['top10_count'].mean() if not top10_frequency.empty else 0
                    },
                    'top_10_frequent': top10_frequency.head(10).to_dict('records') if not top10_frequency.empty else []
                },

                'sector_performance_overview': self._generate_sector_overview(daily_rankings),

                'ranking_distribution': self._analyze_ranking_distribution(daily_rankings)
            }

            self.logger.info("排名统计报告生成完成")
            return report

        except Exception as e:
            self.logger.error(f"生成排名报告时出错: {str(e)}")
            return {'error': str(e)}

    def generate_multiple_window_rankings(self, window_days_list: List[int]) -> Dict[int, Dict]:
        """
        为多个时间窗口生成排名统计数据

        Args:
            window_days_list: 时间窗口天数列表

        Returns:
            字典，键为窗口天数，值为该窗口的排名统计数据
        """
        self.logger.info(f"开始为 {len(window_days_list)} 个时间窗口生成排名统计...")

        results = {}

        for window_days in window_days_list:
            self.logger.info(f"计算 {window_days} 日时间窗口排名统计...")

            try:
                # 统计单日冠军
                champions = self.count_daily_champions(window_days=window_days)

                # 统计前10频次
                top10_frequency = self.count_top10_frequency(window_days=window_days)

                # 存储该时间窗口的结果
                results[window_days] = {
                    'champions': champions,
                    'ranking_frequency': top10_frequency,
                    'window_days': window_days
                }

                # 输出统计信息
                if not champions.empty:
                    top_champion = champions.iloc[0]
                    self.logger.info(f"  {window_days}日窗口冠军统计: {len(champions)}个板块, "
                                   f"最多冠军: {top_champion['sector_name']} ({top_champion['champion_count']}次)")
                else:
                    self.logger.info(f"  {window_days}日窗口冠军统计: 无数据")

                if not top10_frequency.empty:
                    most_frequent = top10_frequency.iloc[0]
                    self.logger.info(f"  {window_days}日窗口前10频次: {len(top10_frequency)}个板块, "
                                   f"最频繁: {most_frequent['sector_name']} ({most_frequent['top10_count']}次)")
                else:
                    self.logger.info(f"  {window_days}日窗口前10频次: 无数据")

            except Exception as e:
                self.logger.error(f"计算 {window_days} 日时间窗口排名统计时出错: {str(e)}")
                results[window_days] = {
                    'champions': pd.DataFrame(),
                    'ranking_frequency': pd.DataFrame(),
                    'window_days': window_days
                }

        self.logger.info(f"多时间窗口排名统计完成，共处理 {len(results)} 个时间窗口")
        return results

    def _generate_sector_overview(self, daily_rankings: pd.DataFrame) -> Dict:
        """
        生成板块表现概览

        Args:
            daily_rankings: 每日排名数据

        Returns:
            板块概览字典
        """
        if daily_rankings.empty:
            return {}

        overview = {}

        for sector_code in self.all_sectors:
            sector_data = daily_rankings[daily_rankings.index.get_level_values('sector_code') == sector_code]

            if not sector_data.empty:
                sector_name = sector_data['sector_name'].iloc[0]

                overview[sector_code] = {
                    'sector_name': sector_name,
                    'total_records': len(sector_data),
                    'avg_rank': sector_data['rank'].mean(),
                    'best_rank': sector_data['rank'].min(),
                    'worst_rank': sector_data['rank'].max(),
                    'avg_change_pct': sector_data['change_pct'].mean(),
                    'champion_days': len(sector_data[sector_data['is_champion'] == True]),
                    'top10_days': len(sector_data[sector_data['is_top10'] == True]),
                    'top5_days': len(sector_data[sector_data['is_top5'] == True]),
                    'top3_days': len(sector_data[sector_data['is_top3'] == True])
                }

        return overview

    def _analyze_ranking_distribution(self, daily_rankings: pd.DataFrame) -> Dict:
        """
        分析排名分布情况

        Args:
            daily_rankings: 每日排名数据

        Returns:
            排名分布分析字典
        """
        if daily_rankings.empty:
            return {}

        # 统计各排名区间的分布
        rank_distribution = {
            'rank_1': len(daily_rankings[daily_rankings['rank'] == 1]),
            'rank_2_5': len(daily_rankings[(daily_rankings['rank'] >= 2) & (daily_rankings['rank'] <= 5)]),
            'rank_6_10': len(daily_rankings[(daily_rankings['rank'] >= 6) & (daily_rankings['rank'] <= 10)]),
            'rank_11_20': len(daily_rankings[(daily_rankings['rank'] >= 11) & (daily_rankings['rank'] <= 20)]),
            'rank_21_50': len(daily_rankings[(daily_rankings['rank'] >= 21) & (daily_rankings['rank'] <= 50)]),
            'rank_50_plus': len(daily_rankings[daily_rankings['rank'] > 50])
        }

        # 计算百分比
        total_records = len(daily_rankings)
        rank_distribution_pct = {k: v / total_records * 100 for k, v in rank_distribution.items()}

        return {
            'distribution_counts': rank_distribution,
            'distribution_percentages': rank_distribution_pct,
            'total_records': total_records
        }

    def get_sector_ranking_history(self, sector_code: str, limit: Optional[int] = None) -> pd.DataFrame:
        """
        获取指定板块的排名历史

        Args:
            sector_code: 板块代码
            limit: 限制返回的记录数，None表示返回全部

        Returns:
            板块排名历史DataFrame
        """
        daily_rankings = self.calculate_daily_rankings()

        if daily_rankings.empty:
            return pd.DataFrame()

        # 筛选指定板块的数据
        sector_history = daily_rankings[daily_rankings.index.get_level_values('sector_code') == sector_code].copy()

        if sector_history.empty:
            self.logger.warning(f"没有找到板块 {sector_code} 的排名历史")
            return pd.DataFrame()

        # 重置索引以便排序
        sector_history = sector_history.reset_index()
        sector_history = sector_history.sort_values('date', ascending=False)

        if limit is not None:
            sector_history = sector_history.head(limit)

        self.logger.info(f"获取板块 {sector_code} 的排名历史，共 {len(sector_history)} 条记录")
        return sector_history

    def find_consecutive_top_performers(self, min_days: int = 3, rank_threshold: int = 10) -> pd.DataFrame:
        """
        查找连续表现优秀的板块

        Args:
            min_days: 最少连续天数
            rank_threshold: 排名阈值（小于等于此排名才算优秀）

        Returns:
            连续表现优秀的板块DataFrame
        """
        self.logger.info(f"查找连续 {min_days} 天排名前 {rank_threshold} 的板块...")

        daily_rankings = self.calculate_daily_rankings()

        if daily_rankings.empty:
            return pd.DataFrame()

        consecutive_performers = []

        for sector_code in self.all_sectors:
            sector_data = daily_rankings[daily_rankings.index.get_level_values('sector_code') == sector_code].copy()

            if sector_data.empty:
                continue

            # 重置索引并按日期排序
            sector_data = sector_data.reset_index().sort_values('date')

            # 标记是否为优秀表现
            sector_data['is_good'] = sector_data['rank'] <= rank_threshold

            # 查找连续的优秀表现期
            consecutive_count = 0
            current_streak = []

            for idx, row in sector_data.iterrows():
                if row['is_good']:
                    consecutive_count += 1
                    current_streak.append(row)
                else:
                    # 检查是否满足最少连续天数
                    if consecutive_count >= min_days:
                        consecutive_performers.append({
                            'sector_code': sector_code,
                            'sector_name': current_streak[0]['sector_name'],
                            'start_date': current_streak[0]['date'],
                            'end_date': current_streak[-1]['date'],
                            'consecutive_days': consecutive_count,
                            'avg_rank': np.mean([r['rank'] for r in current_streak]),
                            'best_rank': min([r['rank'] for r in current_streak]),
                            'avg_change_pct': np.mean([r['change_pct'] for r in current_streak])
                        })

                    consecutive_count = 0
                    current_streak = []

            # 检查最后一个连续期
            if consecutive_count >= min_days:
                consecutive_performers.append({
                    'sector_code': sector_code,
                    'sector_name': current_streak[0]['sector_name'],
                    'start_date': current_streak[0]['date'],
                    'end_date': current_streak[-1]['date'],
                    'consecutive_days': consecutive_count,
                    'avg_rank': np.mean([r['rank'] for r in current_streak]),
                    'best_rank': min([r['rank'] for r in current_streak]),
                    'avg_change_pct': np.mean([r['change_pct'] for r in current_streak])
                })

        if not consecutive_performers:
            self.logger.info("没有找到满足条件的连续表现优秀板块")
            return pd.DataFrame()

        # 转换为DataFrame并排序
        result_df = pd.DataFrame(consecutive_performers)
        result_df = result_df.sort_values('consecutive_days', ascending=False).reset_index(drop=True)

        self.logger.info(f"找到 {len(result_df)} 个连续表现优秀的记录")
        return result_df

    def get_ranking_summary(self) -> Dict:
        """
        获取排名统计摘要

        Returns:
            排名摘要字典
        """
        daily_rankings = self.calculate_daily_rankings()

        if daily_rankings.empty:
            return {'error': '没有排名数据'}

        summary = {
            'data_overview': {
                'total_records': len(daily_rankings),
                'total_trading_days': len(self.available_dates),
                'total_sectors': len(self.all_sectors),
                'date_range': {
                    'start': self.available_dates[0],
                    'end': self.available_dates[-1]
                }
            },
            'ranking_stats': {
                'avg_rank': daily_rankings['rank'].mean(),
                'median_rank': daily_rankings['rank'].median(),
                'avg_change_pct': daily_rankings['change_pct'].mean(),
                'median_change_pct': daily_rankings['change_pct'].median()
            },
            'top_performance_stats': {
                'total_champion_records': len(daily_rankings[daily_rankings['is_champion'] == True]),
                'total_top10_records': len(daily_rankings[daily_rankings['is_top10'] == True]),
                'total_top5_records': len(daily_rankings[daily_rankings['is_top5'] == True]),
                'total_top3_records': len(daily_rankings[daily_rankings['is_top3'] == True])
            }
        }

        return summary

    def clear_cache(self):
        """清除缓存"""
        self.daily_rankings_cache = None
        self.logger.info("排名分析缓存已清除")

    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        return {
            'daily_rankings_cached': self.daily_rankings_cache is not None,
            'cache_size': len(self.daily_rankings_cache) if self.daily_rankings_cache is not None else 0
        }
