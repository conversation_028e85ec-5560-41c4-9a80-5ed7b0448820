#!/usr/bin/env python3
"""
语法测试
"""

print("开始语法测试...")

try:
    # 测试语法
    with open('src/report_generator.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    compile(code, 'src/report_generator.py', 'exec')
    print("✅ 语法测试通过")
    
except SyntaxError as e:
    print(f"❌ 语法错误: {e}")
    print(f"行号: {e.lineno}")
    print(f"文本: {e.text}")
    
except Exception as e:
    print(f"❌ 其他错误: {e}")

print("测试完成")
