#!/usr/bin/env python3
"""
验证一致性分析功能
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_test_data():
    """创建测试数据"""
    
    # 历史时间窗口数据
    historical_data = {
        7: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479', 'BK0739', 'BK0546', 'BK0729'],
            'sector_name': ['水泥建材', '钢铁行业', '工程机械', '玻璃玻纤', '船舶制造'],
            'cumulative_return_pct': [19.05, 17.37, 16.94, 15.65, 15.56]
        }),
        14: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0739', 'BK0479', 'BK0727', 'BK1027'],
            'sector_name': ['水泥建材', '工程机械', '钢铁行业', '医疗服务', '小金属'],
            'cumulative_return_pct': [15.76, 14.10, 12.41, 10.26, 10.15]
        }),
        30: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479', 'BK0739', 'BK0546', 'BK0729'],
            'sector_name': ['水泥建材', '钢铁行业', '工程机械', '玻璃玻纤', '船舶制造'],
            'cumulative_return_pct': [19.05, 17.37, 16.94, 15.65, 15.56]
        })
    }
    
    # 历史排名统计数据
    historical_rankings = {
        7: {
            'champions': pd.DataFrame({
                'sector_code': ['BK0424', 'BK0479', 'BK0739'],
                'sector_name': ['水泥建材', '钢铁行业', '工程机械'],
                'champion_count': [8, 6, 5]
            }),
            'ranking_frequency': pd.DataFrame({
                'sector_code': ['BK0424', 'BK0739', 'BK0479'],
                'sector_name': ['水泥建材', '工程机械', '钢铁行业'],
                'top10_count': [25, 22, 20]
            })
        },
        14: {
            'champions': pd.DataFrame({
                'sector_code': ['BK0424', 'BK0739', 'BK0479'],
                'sector_name': ['水泥建材', '工程机械', '钢铁行业'],
                'champion_count': [10, 7, 5]
            }),
            'ranking_frequency': pd.DataFrame({
                'sector_code': ['BK0424', 'BK0739', 'BK0727'],
                'sector_name': ['水泥建材', '工程机械', '医疗服务'],
                'top10_count': [28, 25, 18]
            })
        }
    }
    
    # 未来时间窗口数据
    future_data = {
        30: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479', 'BK0546'],
            'sector_name': ['水泥建材', '钢铁行业', '玻璃玻纤'],
            'avg_return_pct': [8.5, 6.2, 5.8],
            'consistency_rate': [0.8, 0.6, 0.4]
        }),
        60: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0739', 'BK0727'],
            'sector_name': ['水泥建材', '工程机械', '医疗服务'],
            'avg_return_pct': [7.2, 6.8, 5.5],
            'consistency_rate': [0.75, 0.65, 0.55]
        })
    }
    
    # 未来收益率排行榜数据
    future_rankings = {
        30: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479', 'BK0546', 'BK0729', 'BK1015'],
            'sector_name': ['水泥建材', '钢铁行业', '玻璃玻纤', '船舶制造', '能源金属'],
            'avg_return_pct': [8.5, 6.2, 5.8, 5.2, 4.8],
            'consistency_rate': [0.8, 0.6, 0.4, 0.5, 0.3],
            'rank': [1, 2, 3, 4, 5]
        }),
        60: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0739', 'BK0727', 'BK0479', 'BK1027'],
            'sector_name': ['水泥建材', '工程机械', '医疗服务', '钢铁行业', '小金属'],
            'avg_return_pct': [7.2, 6.8, 5.5, 5.1, 4.9],
            'consistency_rate': [0.75, 0.65, 0.55, 0.45, 0.35],
            'rank': [1, 2, 3, 4, 5]
        }),
        90: pd.DataFrame({
            'sector_code': ['BK0739', 'BK0424', 'BK0479', 'BK0546', 'BK0727'],
            'sector_name': ['工程机械', '水泥建材', '钢铁行业', '玻璃玻纤', '医疗服务'],
            'avg_return_pct': [6.5, 6.2, 5.8, 5.4, 5.0],
            'consistency_rate': [0.7, 0.65, 0.6, 0.5, 0.45],
            'rank': [1, 2, 3, 4, 5]
        })
    }
    
    return {
        'window_performance': historical_data,
        'multiple_window_rankings': historical_rankings,
        'future_window_performance': future_data,
        'future_return_rankings': future_rankings,
        'future_analysis_enabled': True
    }

def main():
    """主函数"""
    print("🚀 开始验证一致性分析功能...")
    
    try:
        # 导入报告生成器
        from src.report_generator import ReportGenerator
        
        print("✅ 成功导入 ReportGenerator")
        
        # 创建测试数据
        print("📊 创建测试数据...")
        analysis_data = create_test_data()
        
        # 创建报告生成器实例
        print("🔧 创建 ReportGenerator 实例...")
        generator = ReportGenerator('test_output')
        
        # 测试一致性分析功能
        print("🔍 测试一致性分析功能...")
        consistency_html = generator._generate_historical_future_consistency_analysis(analysis_data)
        
        if consistency_html:
            print("✅ 一致性分析生成成功！")
            print(f"   HTML长度: {len(consistency_html)} 字符")
            
            # 生成完整的HTML报告
            print("📄 生成完整HTML报告...")
            html_file = generator.generate_html_report(
                analysis_data,
                [],
                "一致性分析功能测试报告"
            )
            
            if html_file:
                print(f"✅ HTML报告已生成: {html_file}")
                
                # 打开报告
                import webbrowser
                webbrowser.open(f'file:///{os.path.abspath(html_file)}')
                print("🌐 已在浏览器中打开报告")
            else:
                print("❌ HTML报告生成失败")
        else:
            print("❌ 一致性分析生成失败")
        
        print("🎉 验证完成！")
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
