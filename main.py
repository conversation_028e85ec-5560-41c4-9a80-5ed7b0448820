#!/usr/bin/env python3
"""
A股板块数据分析程序
主程序入口文件

功能：
1. 历史时间窗口累计涨跌幅分析（7日、14日、30日）
2. 未来时间窗口累计涨跌幅分析（30日、60日、90日）
3. 板块排名频次统计
4. 单日冠军板块统计
5. 历史同期分析

特点：
- 默认启用历史和未来时间窗口分析
- 基于≤2024年数据进行未来回测分析
- 生成综合HTML报告和CSV导出

作者：AI Assistant
创建时间：2025-07-23
更新时间：2025-07-23 (添加默认未来分析功能)
"""

import argparse
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import config


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='A股板块数据分析程序 - 包含历史和未来时间窗口分析',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                           # 运行完整分析（历史+未来时间窗口）
  python main.py --end-date 2023-06-15    # 指定分析参考日期
        """
    )
    
    # 注意：历史和未来时间窗口现在都是固定的，直接使用配置文件中的设置
    parser.add_argument(
        '--windows',
        type=str,
        default=','.join(map(str, config.TIME_WINDOWS)),
        help=argparse.SUPPRESS  # 隐藏此参数，但保留向后兼容性
    )

    parser.add_argument(
        '--future-windows',
        type=str,
        default=','.join(map(str, config.FUTURE_TIME_WINDOWS)),
        help=argparse.SUPPRESS  # 隐藏此参数，但保留向后兼容性
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        default=None,
        help='分析结束日期，格式：YYYY-MM-DD (默认: 最新数据日期)'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default=config.OUTPUT_DIR,
        help=f'输出目录 (默认: {config.OUTPUT_DIR})'
    )
    
    parser.add_argument(
        '--no-charts',
        action='store_true',
        help='禁用图表生成'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        default=True,  # 默认启用详细输出
        help='详细输出模式 (默认启用)'
    )
    
    parser.add_argument(
        '--top-n',
        type=int,
        default=config.TOP_N,
        help=f'排行榜显示数量 (默认: {config.TOP_N})'
    )

    parser.add_argument(
        '--export-csv',
        action='store_true',
        default=True,  # 默认启用CSV导出
        help='导出详细数据到CSV文件 (默认启用)'
    )

    parser.add_argument(
        '--quick-mode',
        action='store_true',
        help='快速模式：只分析最近的数据文件'
    )

    parser.add_argument(
        '--max-files',
        type=int,
        default=None,
        help='限制处理的最大文件数量（用于测试）'
    )

    parser.add_argument(
        '--enable-historical',
        action='store_true',
        default=True,  # 默认启用历史分析
        help='启用历史同期分析功能 (默认启用)'
    )

    return parser.parse_args()


def validate_arguments(args):
    """验证命令行参数"""
    # 使用配置文件中的固定时间窗口设置
    args.windows = config.TIME_WINDOWS
    args.future_windows = config.FUTURE_TIME_WINDOWS

    # 默认启用未来分析
    args.enable_future_analysis = True
    
    # 验证结束日期参数
    if args.end_date:
        try:
            datetime.strptime(args.end_date, '%Y-%m-%d')
        except ValueError:
            print("错误：结束日期格式无效，请使用 YYYY-MM-DD 格式")
            sys.exit(1)
    
    # 验证输出目录
    output_path = Path(args.output_dir)
    if not output_path.exists():
        try:
            output_path.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            print(f"错误：无法创建输出目录 {args.output_dir} - {e}")
            sys.exit(1)
    
    # 验证数据目录
    if not Path(config.DATA_DIR).exists():
        print(f"错误：数据目录 {config.DATA_DIR} 不存在")
        sys.exit(1)

    # 验证top-n参数
    if args.top_n <= 0:
        print("错误：--top-n 参数必须为正整数")
        sys.exit(1)

    # 验证max-files参数
    if args.max_files is not None and args.max_files <= 0:
        print("错误：--max-files 参数必须为正整数")
        sys.exit(1)

    return args


def main():
    """主程序入口"""
    print("=" * 70)
    print("A股板块数据分析程序 - 历史+未来时间窗口分析")
    print("=" * 70)
    
    # 解析和验证参数
    args = parse_arguments()
    args = validate_arguments(args)
    
    if args.verbose:
        print(f"分析配置：")
        print(f"  历史时间窗口: {args.windows}")
        print(f"  未来时间窗口: {args.future_windows}")
        print(f"  分析参考日期: {args.end_date or '最新数据'}")
        print(f"  输出目录: {args.output_dir}")
        print(f"  生成图表: {not args.no_charts}")
        print(f"  排行榜数量: {args.top_n}")
        print(f"  导出CSV: {args.export_csv}")
        print(f"  快速模式: {args.quick_mode}")
        print(f"  历史同期分析: {args.enable_historical}")
        if args.max_files:
            print(f"  最大文件数: {args.max_files}")
        print()
    
    try:
        # 导入所有功能模块
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        from src.ranking_analyzer import RankingAnalyzer
        from src.report_generator import ReportGenerator
        from src.historical_analyzer import HistoricalAnalyzer
        from src.utils import setup_logging
        import pandas as pd
        from tqdm import tqdm
        import time

        # 设置日志
        setup_logging(args.verbose)

        # 开始执行分析
        start_time = time.time()

        print("🚀 开始执行A股板块数据分析（包含历史和未来时间窗口分析）...")
        print()

        # 1. 数据加载阶段
        print("📊 第1步：数据加载")
        print("-" * 50)

        try:
            loader = DataLoader(config.DATA_DIR)
            print(f"发现数据文件: {len(loader.file_list)} 个")

            # 处理快速模式和文件数量限制
            if args.quick_mode:
                # 快速模式：只使用最近的50个文件
                loader.file_list = loader.file_list[-50:]
                print(f"快速模式：使用最近 {len(loader.file_list)} 个文件")
            elif args.max_files:
                # 限制文件数量
                loader.file_list = loader.file_list[-args.max_files:]
                print(f"限制模式：使用最近 {len(loader.file_list)} 个文件")

            # 显示数据基本信息
            date_range = loader.get_date_range()
            print(f"数据日期范围: {date_range[0]} 到 {date_range[1]}")

            # 加载数据
            print("正在加载数据...")
            data = loader.load_all_data()

            # 验证数据
            print("正在验证数据完整性...")

            # 添加调试信息
            print(f"调试信息:")
            print(f"  数据形状: {data.shape}")
            print(f"  索引类型: {type(data.index)}")
            print(f"  索引名称: {data.index.names}")
            print(f"  列名: {list(data.columns)}")

            # 检查可用列（包括索引）
            available_columns = set(data.columns)
            if isinstance(data.index, pd.MultiIndex):
                available_columns.update(data.index.names)
            elif data.index.name:
                available_columns.add(data.index.name)

            required_columns = config.DATA_VALIDATION['required_columns']
            print(f"  必需列: {required_columns}")
            print(f"  可用列: {available_columns}")
            print(f"  缺少列: {set(required_columns) - available_columns}")

            is_valid = loader.validate_data(data)
            if not is_valid:
                print("❌ 数据验证失败，程序终止")
                sys.exit(1)

            # 显示数据摘要
            summary = loader.get_data_summary()
            print(f"✅ 数据加载完成")
            print(f"   总记录数: {summary['total_records']:,}")
            print(f"   板块数量: {summary['total_sectors']}")
            print(f"   内存使用: {summary['memory_usage_mb']:.1f} MB")
            print()

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            if args.verbose:
                import traceback
                traceback.print_exc()
            sys.exit(1)

        # 2. 时间窗口分析阶段
        print("📈 第2步：时间窗口分析（历史+未来）")
        print("-" * 50)

        try:
            analyzer = TimeWindowAnalyzer(data)

            # 确定分析参考日期
            if args.end_date:
                end_date = args.end_date
            else:
                # 使用最新数据日期作为参考日期进行未来时间窗口分析
                # 未来分析通过历史同期数据进行统计预测
                latest_date = analyzer.available_dates[-1]
                end_date = latest_date
                print(f"📅 使用 {end_date.strftime('%Y-%m-%d')} 作为分析参考日期")
                print(f"   未来时间窗口分析将基于过去5年同期历史数据进行统计预测")

            print(f"分析参考日期: {end_date}")
            print(f"历史时间窗口: {args.windows}")
            print(f"未来时间窗口: {args.future_windows}")

            # 执行历史时间窗口分析
            window_results = {}
            for window_days in tqdm(args.windows, desc="历史时间窗口分析", unit="窗口"):
                performance = analyzer.calculate_window_performance(end_date, window_days)
                if not performance.empty:
                    window_results[window_days] = performance
                    top_sector = performance.iloc[0]
                    print(f"  历史{window_days:2d}日窗口最佳: {top_sector['sector_name']} "
                          f"({top_sector['cumulative_return_pct']:.2f}%)")

            # 执行未来时间窗口分析（基于历史同期数据）
            future_window_results = {}
            future_return_rankings = {}
            print(f"\n开始未来时间窗口分析（基于历史同期数据统计预测）...")
            future_window_results = analyzer.calculate_multiple_future_windows(end_date, args.future_windows)

            # 计算收益率排行榜
            print(f"计算未来时间窗口收益率排行榜...")
            future_return_rankings = analyzer.calculate_multiple_future_return_rankings(end_date, args.future_windows, top_n=10)

            # 调试信息：检查收益率排行榜数据
            print(f"收益率排行榜计算完成:")
            for window_days, rankings_data in future_return_rankings.items():
                if not rankings_data.empty:
                    print(f"  {window_days}日窗口: {len(rankings_data)}个板块排行榜")
                else:
                    print(f"  {window_days}日窗口: 无排行榜数据")

            total_windows = len(window_results) + len(future_window_results)
            print(f"✅ 时间窗口分析完成，共分析 {total_windows} 个时间窗口")
            print(f"   - 历史窗口: {len(window_results)} 个")
            print(f"   - 未来窗口: {len(future_window_results)} 个")
            print()

        except Exception as e:
            print(f"❌ 时间窗口分析失败: {str(e)}")
            if args.verbose:
                import traceback
                traceback.print_exc()
            sys.exit(1)

        # 3. 排名统计分析阶段
        print("🏆 第3步：排名统计分析")
        print("-" * 50)

        ranking_analyzer = RankingAnalyzer(data)

        # 为每个历史时间窗口生成排名统计数据
        print(f"为历史时间窗口生成排名统计: {args.windows}")
        multiple_window_rankings = ranking_analyzer.generate_multiple_window_rankings(args.windows)

        # 输出统计摘要
        total_windows = len(multiple_window_rankings)
        print(f"✅ 排名统计分析完成，共处理 {total_windows} 个时间窗口")

        # 显示每个窗口的统计摘要
        for window_days, ranking_data in multiple_window_rankings.items():
            champions = ranking_data.get('champions', pd.DataFrame())
            ranking_frequency = ranking_data.get('ranking_frequency', pd.DataFrame())

            if not champions.empty:
                top_champion = champions.iloc[0]
                print(f"   {window_days}日窗口冠军: {top_champion['sector_name']} "
                      f"({top_champion['champion_count']}次)")

            if not ranking_frequency.empty:
                most_frequent = ranking_frequency.iloc[0]
                print(f"   {window_days}日窗口前10: {most_frequent['sector_name']} "
                      f"({most_frequent['top10_count']}次)")

        print()

        # 3.5. 历史同期分析阶段（可选）
        historical_analysis = {}
        if args.enable_historical:
            print("📈 第3.5步：历史同期分析")
            print("-" * 50)

            historical_analyzer = HistoricalAnalyzer(data)
            target_date = end_date if args.end_date else analyzer.available_dates[-1]

            print(f"目标日期: {target_date}")
            print(f"分析时间窗口: {args.windows}")

            print("正在进行历史同期分析...")
            historical_analysis = historical_analyzer.calculate_historical_windows(
                target_date, args.windows
            )

            if historical_analysis and 'historical_dates' in historical_analysis:
                years_count = len(historical_analysis['historical_dates'])
                print(f"✅ 历史同期分析完成，涵盖 {years_count} 个年份")
            else:
                print("⚠️ 历史数据不足，跳过历史同期分析")

            print()
        else:
            print("⏭️ 跳过历史同期分析（使用 --enable-historical 启用）")
            print()

        # 4. 可视化生成阶段（已禁用）
        generated_charts = []
        print("⏭️ 跳过图表生成（图表功能已禁用）")
        print()

        # 5. CSV导出功能
        if args.export_csv:
            print("💾 第5步：导出数据到CSV文件")
            print("-" * 50)

            csv_files = []

            # 导出时间窗口分析结果
            for window_days, performance in window_results.items():
                if not performance.empty:
                    csv_filename = f"window_performance_{window_days}d.csv"
                    csv_path = Path(args.output_dir) / csv_filename
                    performance.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    csv_files.append(csv_filename)

            # 导出冠军统计结果
            if not champions.empty:
                csv_filename = "champions.csv"
                csv_path = Path(args.output_dir) / csv_filename
                champions.to_csv(csv_path, index=False, encoding='utf-8-sig')
                csv_files.append(csv_filename)

            # 导出前10频次结果
            if not ranking_frequency.empty:
                csv_filename = "ranking_frequency.csv"
                csv_path = Path(args.output_dir) / csv_filename
                ranking_frequency.to_csv(csv_path, index=False, encoding='utf-8-sig')
                csv_files.append(csv_filename)

            print(f"✅ CSV文件导出完成，共导出 {len(csv_files)} 个文件:")
            for csv_file in csv_files:
                print(f"   • {csv_file}")
            print()

        # 5. HTML报告生成
        print("📄 第5步：生成HTML综合报告")
        print("-" * 50)

        report_generator = ReportGenerator(args.output_dir)

        # 准备报告数据
        report_data = {
            'window_performance': window_results,
            'future_window_performance': future_window_results,
            'future_return_rankings': future_return_rankings,  # 新增收益率排行榜数据
            'multiple_window_rankings': multiple_window_rankings,  # 多时间窗口排名统计数据
            'historical_analysis': historical_analysis,
            'future_analysis_enabled': True  # 默认启用未来分析
        }

        # 生成HTML报告
        html_file = report_generator.generate_html_report(
            report_data,
            generated_charts,
            "A股板块数据分析报告（历史+未来时间窗口分析）"
        )

        if html_file:
            print(f"✅ HTML报告已生成: {os.path.basename(html_file)}")
        else:
            print("⚠️ HTML报告生成失败")
        print()

        # 6. 结果输出阶段
        print("📋 第6步：输出分析结果")
        print("-" * 50)

        try:
            # 输出历史时间窗口分析结果
            print("🔍 历史时间窗口累计涨跌幅分析结果:")
            print("=" * 80)

            for window_days in sorted(window_results.keys()):
                performance = window_results[window_days]
                if not performance.empty:
                    print(f"\n📊 历史{window_days}日时间窗口表现排行榜 (前{args.top_n}名):")
                    print("-" * 75)
                    print(f"{'排名':<4} {'板块名称':<12} {'板块代码':<8} {'累计收益率':<10} {'平均日收益率':<12} {'波动率':<8}")
                    print("-" * 75)

                    top_performers = performance.head(args.top_n)
                    for idx, row in top_performers.iterrows():
                        print(f"{idx+1:<4} {row['sector_name']:<12} {row['sector_code']:<8} "
                              f"{row['cumulative_return_pct']:>8.2f}%  {row['avg_daily_change_pct']:>10.2f}%  "
                              f"{row['volatility']:>6.2f}%")

            # # 输出未来时间窗口分析结果
            # print(f"\n🔮 未来时间窗口累计涨跌幅分析结果（基于≤{config.DATA_YEAR_LIMIT['future_analysis_max_year']}年数据）:")
            # print("=" * 80)

            # for window_days in sorted(future_window_results.keys()):
            #     performance = future_window_results[window_days]
            #     if not performance.empty:
            #         print(f"\n📊 未来{window_days}日时间窗口表现排行榜 (前{args.top_n}名):")
            #         print("-" * 75)
            #         print(f"{'排名':<4} {'板块名称':<12} {'板块代码':<8} {'累计收益率':<10} {'平均日收益率':<12} {'波动率':<8}")
            #         print("-" * 75)

            #         top_performers = performance.head(args.top_n)
            #         for idx, row in top_performers.iterrows():
            #             print(f"{idx+1:<4} {row['sector_name']:<12} {row['sector_code']:<8} "
            #                   f"{row['cumulative_return_pct']:>8.2f}%  {row['avg_daily_change_pct']:>10.2f}%  "
            #                   f"{row['volatility']:>6.2f}%")

            # print("\n" + "=" * 80)

        except Exception as e:
            print(f"❌ 结果输出失败: {str(e)}")
            if args.verbose:
                import traceback
                traceback.print_exc()
            sys.exit(1)

        try:
            # 输出单日冠军统计结果
            print("\n🏆 单日冠军板块统计结果:")
            print("=" * 80)

            if not champions.empty:
                print(f"\n📈 单日冠军次数排行榜 (前{args.top_n}名):")
                print("-" * 85)
                print(f"{'排名':<4} {'板块名称':<12} {'板块代码':<8} {'冠军次数':<8} {'冠军频率':<10} {'平均冠军涨跌幅':<12}")
                print("-" * 85)

                top_champions = champions.head(args.top_n)
                for idx, row in top_champions.iterrows():
                    print(f"{row['rank']:<4} {row['sector_name']:<12} {row['sector_code']:<8} "
                          f"{row['champion_count']:>6}次  {row['champion_frequency_pct']:>8.2f}%  "
                          f"{row['avg_champion_change_pct']:>10.2f}%")
            else:
                print("⚠️ 没有找到冠军数据")

            print("\n" + "=" * 80)

            # 输出前10频次统计结果
            print("\n📊 板块排名频次统计结果:")
            print("=" * 80)

            if not ranking_frequency.empty:
                print(f"\n📋 前10名频次排行榜 (前{args.top_n}名):")
                print("-" * 95)
                print(f"{'排名':<4} {'板块名称':<12} {'板块代码':<8} {'前10次数':<8} {'前10频率':<10} "
                      f"{'前5次数':<8} {'前3次数':<8} {'冠军次数':<8}")
                print("-" * 95)

                top_frequent = ranking_frequency.head(args.top_n)
                for idx, row in top_frequent.iterrows():
                    print(f"{row['rank']:<4} {row['sector_name']:<12} {row['sector_code']:<8} "
                          f"{row['top10_count']:>6}次  {row['top10_frequency_pct']:>8.2f}%  "
                          f"{row['top5_count']:>6}次  {row['top3_count']:>6}次  "
                          f"{row['champion_count']:>6}次")
            else:
                print("⚠️ 没有找到前10频次数据")

            print("\n" + "=" * 80)

            # 输出数据摘要
            print("\n📈 数据分析摘要:")
            print("=" * 80)

            # 计算执行时间
            execution_time = time.time() - start_time

            print(f"✅ 分析完成！")
            print(f"📊 数据概览:")
            print(f"   • 分析数据量: {summary['total_records']:,} 条记录")
            print(f"   • 板块总数: {summary['total_sectors']} 个")
            print(f"   • 交易日数: {len(analyzer.available_dates)} 天")
            print(f"   • 数据日期范围: {date_range[0]} 到 {date_range[1]}")

            print(f"\n🔍 分析结果:")
            total_windows = len(window_results) + len(future_window_results)
            print(f"   • 时间窗口分析: {total_windows} 个时间窗口")
            print(f"     - 历史窗口: {len(window_results)} 个")
            print(f"     - 未来窗口: {len(future_window_results)} 个")
            print(f"   • 获得冠军的板块: {len(champions)} 个")
            print(f"   • 进入前10的板块: {len(ranking_frequency)} 个")

            if not args.no_charts and 'generated_charts' in locals():
                print(f"   • 生成图表文件: {len(generated_charts)} 个")

            print(f"\n⏱️ 执行信息:")
            print(f"   • 总执行时间: {execution_time:.2f} 秒")
            print(f"   • 内存使用: {summary['memory_usage_mb']:.1f} MB")
            print(f"   • 输出目录: {args.output_dir}")

            # 输出最佳表现摘要
            if window_results:
                print(f"\n🌟 最佳表现摘要:")
                best_overall = None
                best_return = float('-inf')

                for window_days, performance in window_results.items():
                    if not performance.empty:
                        top_performer = performance.iloc[0]
                        if top_performer['cumulative_return_pct'] > best_return:
                            best_return = top_performer['cumulative_return_pct']
                            best_overall = (window_days, top_performer)

                if best_overall:
                    window_days, performer = best_overall
                    print(f"   • 最佳整体表现: {performer['sector_name']} "
                          f"({window_days}日窗口, {performer['cumulative_return_pct']:.2f}%)")

                if not champions.empty:
                    most_champion = champions.iloc[0]
                    print(f"   • 最多冠军板块: {most_champion['sector_name']} "
                          f"({most_champion['champion_count']}次)")

                if not ranking_frequency.empty:
                    most_frequent = ranking_frequency.iloc[0]
                    print(f"   • 最频繁前10板块: {most_frequent['sector_name']} "
                          f"({most_frequent['top10_count']}次)")

            # CSV导出功能
            # if args.export_csv:
            #     print(f"\n💾 导出数据到CSV文件:")
            #     print("-" * 50)

            #     try:
            #         csv_files = []

            #         # 导出时间窗口分析结果
            #         for window_days, performance in window_results.items():
            #             if not performance.empty:
            #                 csv_filename = f"window_performance_{window_days}d.csv"
            #                 csv_path = Path(args.output_dir) / csv_filename
            #                 performance.to_csv(csv_path, index=False, encoding='utf-8-sig')
            #                 csv_files.append(csv_filename)

            #         # 导出冠军统计结果
            #         if not champions.empty:
            #             csv_filename = "champions.csv"
            #             csv_path = Path(args.output_dir) / csv_filename
            #             champions.to_csv(csv_path, index=False, encoding='utf-8-sig')
            #             csv_files.append(csv_filename)

            #         # 导出前10频次结果
            #         if not ranking_frequency.empty:
            #             csv_filename = "ranking_frequency.csv"
            #             csv_path = Path(args.output_dir) / csv_filename
            #             ranking_frequency.to_csv(csv_path, index=False, encoding='utf-8-sig')
            #             csv_files.append(csv_filename)

            #         print(f"✅ CSV文件导出完成，共导出 {len(csv_files)} 个文件:")
            #         for csv_file in csv_files:
            #             print(f"   • {csv_file}")

            #     except Exception as e:
            #         print(f"❌ CSV导出失败: {str(e)}")
            #         if args.verbose:
            #             import traceback
            #             traceback.print_exc()

            # HTML报告生成
            print(f"\n📄 生成HTML综合报告:")
            print("-" * 50)

            try:
                report_generator = ReportGenerator(args.output_dir)

                # 准备报告数据
                report_data = {
                    'window_performance': window_results,
                    'future_window_performance': future_window_results,
                    'future_return_rankings': future_return_rankings,  # 新增收益率排行榜数据
                    'multiple_window_rankings': multiple_window_rankings,  # 多时间窗口排名统计数据
                    'historical_analysis': historical_analysis,
                    'future_analysis_enabled': bool(future_window_results),
                    'reference_date': end_date  # 添加参考日期
                }

                # 生成HTML报告
                html_file = report_generator.generate_html_report(
                    report_data,
                    generated_charts if 'generated_charts' in locals() else [],
                    "A股板块数据分析报告"
                )

                if html_file:
                    print(f"✅ HTML报告已生成: {os.path.basename(html_file)}")
                else:
                    print("⚠️ HTML报告生成失败")

            except Exception as e:
                print(f"❌ HTML报告生成失败: {str(e)}")
                if args.verbose:
                    import traceback
                    traceback.print_exc()

            print("\n" + "=" * 80)
            print("🎉 A股板块数据分析程序执行完成！")
            print("=" * 80)

            print("\n" + "=" * 80)
            print("🎉 A股板块数据分析程序执行完成！")
            print("=" * 80)
            print(f"📊 数据概览:")
            print(f"   • 分析数据量: {summary['total_records']:,} 条记录")
            print(f"   • 板块总数: {summary['total_sectors']} 个")
            print(f"   • 交易日数: {len(analyzer.available_dates)} 天")
            print(f"   • 数据日期范围: {date_range[0]} 到 {date_range[1]}")

            print(f"\n🔍 分析结果:")
            total_windows_final = len(window_results) + len(future_window_results)
            print(f"   • 时间窗口分析: {total_windows_final} 个时间窗口")
            print(f"     - 历史窗口: {len(window_results)} 个")
            print(f"     - 未来窗口: {len(future_window_results)} 个")
            print(f"   • 获得冠军的板块: {len(champions)} 个")
            print(f"   • 进入前10的板块: {len(ranking_frequency)} 个")

            if generated_charts:
                print(f"   • 生成图表文件: {len(generated_charts)} 个")

            print(f"\n⏱️ 执行信息:")
            print(f"   • 总执行时间: {time.time() - start_time:.2f} 秒")
            print(f"   • 内存使用: {summary['memory_usage_mb']:.1f} MB")
            print(f"   • 输出目录: {args.output_dir}")
            print("\n" + "=" * 80)

        except Exception as e:
            print(f"❌ 结果输出失败: {str(e)}")
            if args.verbose:
                import traceback
                traceback.print_exc()
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序执行出错：{e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
