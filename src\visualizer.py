"""
数据可视化模块
已禁用所有图表功能，仅保留数据处理接口
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Union
import logging
import os
import sys
from datetime import datetime
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

# 忽略警告
warnings.filterwarnings('ignore', category=UserWarning)

class Visualizer:
    """可视化器类 - 已禁用图表功能"""

    def __init__(self, output_dir: str = 'output'):
        """
        初始化可视化器

        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.logger = self._setup_logger()

        self.logger.info(f"可视化器初始化完成（图表功能已禁用），输出目录: {self.output_dir}")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def plot_window_performance(self, data: pd.DataFrame, window_days: int,
                               top_n: int = 15, title: str = None) -> str:
        """
        绘制时间窗口表现图（已禁用）

        Args:
            data: 时间窗口表现数据
            window_days: 时间窗口天数
            top_n: 显示前N名
            title: 自定义标题

        Returns:
            空字符串（图表功能已禁用）
        """
        self.logger.info(f"图表功能已禁用，跳过时间窗口表现图生成")
        return ""

    def plot_ranking_frequency(self, frequency_data: pd.DataFrame,
                              top_n: int = 15, title: str = None) -> str:
        """
        绘制排名频次图（已禁用）

        Args:
            frequency_data: 排名频次数据
            top_n: 显示前N名
            title: 自定义标题

        Returns:
            空字符串（图表功能已禁用）
        """
        self.logger.info(f"图表功能已禁用，跳过排名频次图生成")
        return ""

    def plot_champion_distribution(self, champion_data: pd.DataFrame,
                                  top_n: int = 12, title: str = None) -> str:
        """
        绘制冠军分布图（已禁用）

        Args:
            champion_data: 冠军统计数据
            top_n: 显示前N名
            title: 自定义标题

        Returns:
            空字符串（图表功能已禁用）
        """
        self.logger.info(f"图表功能已禁用，跳过冠军分布图生成")
        return ""

    def plot_time_series_trends(self, data: pd.DataFrame, sectors: List[str],
                               metric: str = 'change_pct', title: str = None) -> str:
        """
        绘制时间序列趋势图（已禁用）

        Args:
            data: 包含时间序列数据的DataFrame
            sectors: 要显示的板块列表
            metric: 要显示的指标
            title: 自定义标题

        Returns:
            空字符串（图表功能已禁用）
        """
        self.logger.info(f"图表功能已禁用，跳过时间序列趋势图生成")
        return ""

    def generate_comprehensive_report(self, all_data: Dict,
                                    report_title: str = "A股板块数据分析综合报告") -> List[str]:
        """
        生成综合报告（已禁用图表功能）

        Args:
            all_data: 包含所有分析数据的字典
            report_title: 报告标题

        Returns:
            空列表（图表功能已禁用）
        """
        self.logger.info("图表功能已禁用，跳过综合可视化报告生成")
        return []

    def get_chart_info(self) -> Dict:
        """获取图表生成信息"""
        return {
            'output_dir': str(self.output_dir),
            'chart_enabled': False,
            'message': '图表功能已禁用'
        }
