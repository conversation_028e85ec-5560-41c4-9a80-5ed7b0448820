#!/usr/bin/env python3
"""
测试一致性分析功能
"""

import sys
import os
import pandas as pd

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.report_generator import ReportGenerator
    
    print("创建测试数据...")
    
    # 创建模拟的历史数据
    historical_data = {
        7: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479', 'BK0739'],
            'sector_name': ['水泥建材', '钢铁行业', '工程机械'],
            'cumulative_return_pct': [19.05, 17.37, 16.94]
        }),
        14: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479', 'BK0739'],
            'sector_name': ['水泥建材', '钢铁行业', '工程机械'],
            'cumulative_return_pct': [15.76, 12.41, 14.10]
        })
    }
    
    # 创建模拟的历史排名数据
    historical_rankings = {
        7: {
            'champions': pd.DataFrame({
                'sector_code': ['BK0424', 'BK0479'],
                'sector_name': ['水泥建材', '钢铁行业'],
                'champion_count': [5, 3]
            }),
            'ranking_frequency': pd.DataFrame({
                'sector_code': ['BK0424', 'BK0739'],
                'sector_name': ['水泥建材', '工程机械'],
                'top10_count': [15, 12]
            })
        }
    }
    
    # 创建模拟的未来数据
    future_data = {
        30: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479'],
            'sector_name': ['水泥建材', '钢铁行业'],
            'avg_return_pct': [8.5, 6.2]
        })
    }
    
    # 创建模拟的未来排名数据
    future_rankings = {
        30: pd.DataFrame({
            'sector_code': ['BK0424', 'BK0479', 'BK0546'],
            'sector_name': ['水泥建材', '钢铁行业', '玻璃玻纤'],
            'avg_return_pct': [8.5, 6.2, 5.8],
            'consistency_rate': [0.8, 0.6, 0.4]
        })
    }
    
    print("创建ReportGenerator实例...")
    generator = ReportGenerator()
    
    print("测试一致性分析...")
    analysis_data = {
        'window_performance': historical_data,
        'multiple_window_rankings': historical_rankings,
        'future_window_performance': future_data,
        'future_return_rankings': future_rankings
    }
    
    # 测试一致性分析方法
    consistency_html = generator._generate_historical_future_consistency_analysis(analysis_data)
    
    if consistency_html:
        print("一致性分析生成成功！")
        print(f"HTML长度: {len(consistency_html)} 字符")
        
        # 保存测试结果
        with open('test_consistency_output.html', 'w', encoding='utf-8') as f:
            f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>一致性分析测试</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .section {{ margin-bottom: 30px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .consistency-rate.excellent {{ background-color: #28a745; color: white; padding: 4px 8px; border-radius: 12px; }}
        .consistency-rate.good {{ background-color: #ffc107; color: #212529; padding: 4px 8px; border-radius: 12px; }}
        .consistency-rate.fair {{ background-color: #6c757d; color: white; padding: 4px 8px; border-radius: 12px; }}
        .rank-badge {{ display: inline-block; padding: 4px 8px; border-radius: 50%; font-weight: bold; }}
        .rank-badge.rank-1 {{ background-color: #ffd700; color: #333; }}
        .rank-badge.rank-2 {{ background-color: #c0c0c0; color: #333; }}
        .rank-badge.rank-3 {{ background-color: #cd7f32; color: white; }}
        .rank-badge.rank-other {{ background-color: #6c757d; color: white; }}
        .analysis-note {{ background-color: #e8f4fd; border-left: 4px solid #3498db; padding: 15px; margin: 15px 0; }}
        .consistency-note {{ background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; }}
        .no-consistency {{ text-align: center; color: #6c757d; font-style: italic; padding: 20px; background-color: #f8f9fa; }}
    </style>
</head>
<body>
    <h1>一致性分析测试结果</h1>
    {consistency_html}
</body>
</html>
            """)
        
        print("测试结果已保存到 test_consistency_output.html")
    else:
        print("一致性分析生成失败")
    
    print("测试完成！")
    
except Exception as e:
    print(f"测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
