#!/usr/bin/env python3
"""
调试年份收益率数据结构
"""

import pandas as pd
import numpy as np

# 模拟一个sector数据
sector_data = {
    'sector_name': '石油行业',
    'historical_years': [2020, 2021, 2022, 2023, 2024],
    'year_returns_dict': {
        2020: 4.65,
        2021: 3.84,
        2022: 2.32,
        2023: 1.15,
        2024: 7.75
    }
}

print("调试年份收益率格式化逻辑:")
print(f"板块名称: {sector_data['sector_name']}")
print(f"历史年份: {sector_data['historical_years']}")
print(f"年份收益率映射: {sector_data['year_returns_dict']}")

# 模拟报告生成器中的逻辑
historical_years = sector_data.get('historical_years', [])
year_returns_dict = sector_data.get('year_returns_dict', {})

print(f"\n检查数据类型:")
print(f"historical_years 类型: {type(historical_years)}")
print(f"year_returns_dict 类型: {type(year_returns_dict)}")
print(f"year_returns_dict 是否为空: {not year_returns_dict}")

if isinstance(historical_years, list) and year_returns_dict:
    print("\n✅ 进入年份+收益率格式化分支")
    # 格式化为：年份(收益率%)
    years_with_returns = []
    for year in sorted(historical_years):
        if year in year_returns_dict:
            return_rate = year_returns_dict[year]
            years_with_returns.append(f"{year}({return_rate:.1f}%)")
        else:
            years_with_returns.append(str(year))
    years_str = ', '.join(years_with_returns)
elif isinstance(historical_years, list):
    print("\n❌ 进入仅年份格式化分支")
    # 如果没有收益率数据，只显示年份
    years_str = ', '.join(map(str, sorted(historical_years)))
else:
    print("\n❌ 进入默认格式化分支")
    years_str = str(historical_years)

print(f"\n最终格式化结果: {years_str}")

# 测试空的year_returns_dict情况
print("\n" + "="*60)
print("测试空的year_returns_dict情况:")

sector_data_empty = {
    'sector_name': '证券',
    'historical_years': [2020, 2021, 2022, 2023, 2024],
    'year_returns_dict': {}
}

historical_years = sector_data_empty.get('historical_years', [])
year_returns_dict = sector_data_empty.get('year_returns_dict', {})

print(f"year_returns_dict 是否为空: {not year_returns_dict}")

if isinstance(historical_years, list) and year_returns_dict:
    print("✅ 进入年份+收益率格式化分支")
    years_with_returns = []
    for year in sorted(historical_years):
        if year in year_returns_dict:
            return_rate = year_returns_dict[year]
            years_with_returns.append(f"{year}({return_rate:.1f}%)")
        else:
            years_with_returns.append(str(year))
    years_str = ', '.join(years_with_returns)
elif isinstance(historical_years, list):
    print("❌ 进入仅年份格式化分支")
    years_str = ', '.join(map(str, sorted(historical_years)))
else:
    years_str = str(historical_years)

print(f"最终格式化结果: {years_str}")
