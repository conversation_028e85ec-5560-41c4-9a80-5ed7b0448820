#!/usr/bin/env python3
"""
测试导入模块
"""

try:
    print("开始测试导入...")
    
    # 测试基本导入
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
    
    print("导入 pandas...")
    import pandas as pd
    
    print("导入 config...")
    import config
    
    print("导入 report_generator...")
    from src.report_generator import ReportGenerator
    
    print("创建 ReportGenerator 实例...")
    generator = ReportGenerator()
    
    print("测试成功！")
    
except Exception as e:
    print(f"测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
