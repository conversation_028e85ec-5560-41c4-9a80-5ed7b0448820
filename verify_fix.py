#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证窗口数统计修复
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def verify_fix():
    """验证修复效果"""
    print("🔧 验证窗口数统计修复")
    print("=" * 40)
    
    try:
        from report_generator import ReportGenerator
        
        # 创建测试数据
        historical_info = {
            'sector_name': '煤炭行业',
            'appearances': [
                {'type': 'historical_performance', 'window_days': 7, 'rank': 4, 'score': 7, 'return_pct': 5.8},
                {'type': 'historical_performance', 'window_days': 14, 'rank': 7, 'score': 4, 'return_pct': 9.4},
                {'type': 'frequency_ranking', 'window_days': 7, 'rank': 2, 'score': 9, 'top10_count': 3},
                {'type': 'frequency_ranking', 'window_days': 14, 'rank': 9, 'score': 2, 'top10_count': 4},
            ],
            'total_score': 22
        }
        
        future_info = {
            'sector_name': '煤炭行业',
            'appearances': [
                {'type': 'future_return_ranking', 'window_days': 30, 'rank': 11, 'score': 0, 'avg_return_pct': 7.8},
                {'type': 'future_return_ranking', 'window_days': 60, 'rank': 11, 'score': 0, 'avg_return_pct': 9.3},
                {'type': 'future_consistency_ranking', 'window_days': 30, 'rank': 11, 'score': 0, 'consistency_rate': 60.0},
                {'type': 'future_consistency_ranking', 'window_days': 60, 'rank': 11, 'score': 0, 'consistency_rate': 80.0},
                {'type': 'future_consistency_ranking', 'window_days': 90, 'rank': 11, 'score': 0, 'consistency_rate': 100.0},
            ],
            'total_score': 0
        }
        
        # 模拟计算过程
        historical_score = historical_info['total_score']
        future_score = future_info['total_score']
        
        # 修复后的逻辑
        historical_appearances_count = len(historical_info['appearances'])
        future_appearances_count = len(future_info['appearances'])
        
        total_possible_appearances = 18
        appearance_coverage = (historical_appearances_count + future_appearances_count) / total_possible_appearances
        overlap_score = (historical_score + future_score) * appearance_coverage
        
        print(f"📊 煤炭行业数据验证:")
        print(f"  历史项目数: {historical_appearances_count}")
        print(f"  未来项目数: {future_appearances_count}")
        print(f"  历史得分: {historical_score}")
        print(f"  未来得分: {future_score}")
        print(f"  项目覆盖度: {appearance_coverage:.3f}")
        print(f"  重合度评分: {overlap_score:.1f}")
        
        # 验证详细信息
        generator = ReportGenerator("output")
        test_sector = {
            'sector_name': '煤炭行业',
            'historical_appearances': historical_info['appearances'],
            'future_appearances': future_info['appearances']
        }
        
        detail_info = generator._generate_sector_detail_info(test_sector)
        print(f"\n📝 详细信息:")
        print(f"  {detail_info}")
        
        # 分析详细信息项目数
        parts = detail_info.split(' | ')
        if len(parts) >= 2:
            historical_part = parts[0].replace('历史: ', '')
            future_part = parts[1].replace('未来: ', '')
            
            historical_items = [item.strip() for item in historical_part.split(';') if item.strip()]
            future_items = [item.strip() for item in future_part.split(';') if item.strip()]
            
            print(f"\n✅ 一致性验证:")
            print(f"  表格历史项目数: {historical_appearances_count}")
            print(f"  详细历史项目数: {len(historical_items)}")
            print(f"  表格未来项目数: {future_appearances_count}")
            print(f"  详细未来项目数: {len(future_items)}")
            
            if historical_appearances_count == len(historical_items) and future_appearances_count == len(future_items):
                print(f"  🎉 数据一致性验证通过！")
                return True
            else:
                print(f"  ❌ 数据一致性验证失败！")
                return False
        else:
            print(f"  ⚠️ 详细信息格式异常")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_fix()
    if success:
        print("\n🎉 修复验证成功！")
    else:
        print("\n❌ 修复验证失败！")
