"""
工具函数模块
提供通用的辅助功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Tuple, Optional
import logging
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

def setup_logging(verbose: bool = False) -> logging.Logger:
    """
    设置日志配置

    Args:
        verbose: 是否启用详细日志

    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger('stock_analysis')

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 设置日志级别
    level = logging.DEBUG if verbose else logging.INFO
    logger.setLevel(level)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)

    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(console_handler)

    return logger

def validate_date_format(date_str: str, format_str: str = '%Y-%m-%d') -> bool:
    """
    验证日期格式

    Args:
        date_str: 日期字符串
        format_str: 期望的日期格式

    Returns:
        是否符合格式
    """
    try:
        datetime.strptime(date_str, format_str)
        return True
    except ValueError:
        return False

def format_percentage(value: float, decimal_places: int = 2) -> str:
    """
    格式化百分比

    Args:
        value: 数值
        decimal_places: 小数位数

    Returns:
        格式化后的百分比字符串
    """
    if pd.isna(value):
        return "N/A"
    return f"{value:.{decimal_places}f}%"

def format_number(value: float, decimal_places: int = 2) -> str:
    """
    格式化数字，添加千分位分隔符

    Args:
        value: 数值
        decimal_places: 小数位数

    Returns:
        格式化后的数字字符串
    """
    if pd.isna(value):
        return "N/A"
    return f"{value:,.{decimal_places}f}"

def calculate_business_days(start_date: str, days: int) -> str:
    """
    计算工作日（向前或向后）

    Args:
        start_date: 起始日期 (YYYY-MM-DD)
        days: 天数（正数向后，负数向前）

    Returns:
        计算后的日期字符串
    """
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')

        # 简单的工作日计算（不考虑节假日）
        current = start
        remaining_days = abs(days)
        direction = 1 if days > 0 else -1

        while remaining_days > 0:
            current += timedelta(days=direction)
            # 跳过周末
            if current.weekday() < 5:  # 0-4 是周一到周五
                remaining_days -= 1

        return current.strftime('%Y-%m-%d')
    except ValueError:
        return start_date

def get_trading_days(start_date: str, end_date: str) -> List[str]:
    """
    获取交易日列表（简化版，不考虑节假日）

    Args:
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        交易日期列表
    """
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')

        trading_days = []
        current = start

        while current <= end:
            # 只包含工作日
            if current.weekday() < 5:
                trading_days.append(current.strftime('%Y-%m-%d'))
            current += timedelta(days=1)

        return trading_days
    except ValueError:
        return []

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误

    Args:
        numerator: 分子
        denominator: 分母
        default: 默认值

    Returns:
        除法结果或默认值
    """
    if pd.isna(numerator) or pd.isna(denominator) or denominator == 0:
        return default
    return numerator / denominator

def memory_usage_mb(df: pd.DataFrame) -> float:
    """
    计算DataFrame的内存使用量（MB）

    Args:
        df: DataFrame

    Returns:
        内存使用量（MB）
    """
    return df.memory_usage(deep=True).sum() / 1024 / 1024

def print_dataframe_info(df: pd.DataFrame, name: str = "DataFrame"):
    """
    打印DataFrame的基本信息

    Args:
        df: DataFrame
        name: DataFrame名称
    """
    print(f"\n{name} 信息:")
    print(f"  形状: {df.shape}")
    print(f"  内存使用: {memory_usage_mb(df):.2f} MB")
    print(f"  列名: {list(df.columns)}")
    if hasattr(df.index, 'names') and df.index.names[0] is not None:
        print(f"  索引: {df.index.names}")
    print(f"  数据类型:")
    for col, dtype in df.dtypes.items():
        print(f"    {col}: {dtype}")
    print()
