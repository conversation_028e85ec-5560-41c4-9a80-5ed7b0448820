#!/usr/bin/env python3
"""
测试排名分析器的时间窗口功能
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.data_loader import DataLoader
    from src.ranking_analyzer import RankingAnalyzer
    import config
    
    print("=" * 60)
    print("测试排名分析器的时间窗口功能")
    print("=" * 60)
    
    # 1. 加载少量数据进行测试
    print("1. 加载测试数据...")
    loader = DataLoader(config.DATA_DIR)
    
    # 只使用最近的10个文件进行测试
    loader.file_list = loader.file_list[-10:]
    print(f"使用最近 {len(loader.file_list)} 个数据文件")
    
    data = loader.load_all_data()
    print(f"数据形状: {data.shape}")
    
    # 2. 创建排名分析器
    print("\n2. 创建排名分析器...")
    ranking_analyzer = RankingAnalyzer(data)
    
    # 3. 测试不同时间窗口的单日冠军统计
    print("\n3. 测试单日冠军统计...")
    
    # 测试全历史数据
    print("\n3.1 全历史数据:")
    champions_all = ranking_analyzer.count_daily_champions()
    if not champions_all.empty:
        print(f"  获得冠军的板块数: {len(champions_all)}")
        print(f"  最多冠军: {champions_all.iloc[0]['sector_name']} ({champions_all.iloc[0]['champion_count']}次)")
        print(f"  分析天数: {champions_all.iloc[0]['analysis_days']}")
    
    # 测试7日窗口
    print("\n3.2 最近7日:")
    champions_7d = ranking_analyzer.count_daily_champions(window_days=7)
    if not champions_7d.empty:
        print(f"  获得冠军的板块数: {len(champions_7d)}")
        print(f"  最多冠军: {champions_7d.iloc[0]['sector_name']} ({champions_7d.iloc[0]['champion_count']}次)")
        print(f"  分析天数: {champions_7d.iloc[0]['analysis_days']}")
    else:
        print("  没有冠军数据")
    
    # 测试14日窗口
    print("\n3.3 最近14日:")
    champions_14d = ranking_analyzer.count_daily_champions(window_days=14)
    if not champions_14d.empty:
        print(f"  获得冠军的板块数: {len(champions_14d)}")
        print(f"  最多冠军: {champions_14d.iloc[0]['sector_name']} ({champions_14d.iloc[0]['champion_count']}次)")
        print(f"  分析天数: {champions_14d.iloc[0]['analysis_days']}")
    else:
        print("  没有冠军数据")
    
    # 4. 测试前10频次统计
    print("\n4. 测试前10频次统计...")
    
    # 测试7日窗口
    print("\n4.1 最近7日:")
    top10_7d = ranking_analyzer.count_top10_frequency(window_days=7)
    if not top10_7d.empty:
        print(f"  进入前10的板块数: {len(top10_7d)}")
        print(f"  最频繁前10: {top10_7d.iloc[0]['sector_name']} ({top10_7d.iloc[0]['top10_count']}次)")
        print(f"  分析天数: {top10_7d.iloc[0]['analysis_days']}")
    else:
        print("  没有前10数据")
    
    # 测试14日窗口
    print("\n4.2 最近14日:")
    top10_14d = ranking_analyzer.count_top10_frequency(window_days=14)
    if not top10_14d.empty:
        print(f"  进入前10的板块数: {len(top10_14d)}")
        print(f"  最频繁前10: {top10_14d.iloc[0]['sector_name']} ({top10_14d.iloc[0]['top10_count']}次)")
        print(f"  分析天数: {top10_14d.iloc[0]['analysis_days']}")
    else:
        print("  没有前10数据")
    
    # 5. 测试HTML报告数据结构
    print("\n5. 测试HTML报告数据结构...")
    
    # 模拟报告数据
    report_data = {
        'champions': champions_7d,
        'ranking_frequency': top10_7d,
        'ranking_window_days': 7
    }
    
    # 测试时间跨度文本生成
    ranking_window_days = report_data.get('ranking_window_days', None)
    time_span_text = f"（最近{ranking_window_days}日）" if ranking_window_days else ""
    
    print(f"  时间跨度文本: '{time_span_text}'")
    print(f"  单日冠军标题: '单日冠军次数排行榜{time_span_text}'")
    print(f"  前10频次标题: '前10名频次排行榜{time_span_text}'")
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！排名分析器的时间窗口功能正常工作")
    print("=" * 60)
    
except Exception as e:
    print(f"❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
