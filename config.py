"""
A股板块数据分析程序配置文件
"""

import os

# 数据目录配置
DATA_DIR = 'data'

# 时间窗口配置（天数）
TIME_WINDOWS = [7, 14, 30]

# 未来时间窗口配置（天数）- 用于前瞻性分析
FUTURE_TIME_WINDOWS = [30, 60, 90]

# 数据年份限制配置
DATA_YEAR_LIMIT = {
    'future_analysis_max_year': 2025,  # 未来时间窗口分析的最大年份
    'exclude_current_year': False,     # 是否排除当前年份（2025年）
    'analysis_years_count': 5          # 分析年份数（用于一致性率计算的分母）
}

# 排行榜显示数量
TOP_N = 10

# 输出目录配置
OUTPUT_DIR = 'output'

# 图表输出配置
CHART_CONFIG = {
    'figsize': (12, 8),
    'dpi': 300,
    'format': 'png',
    'style': 'seaborn-v0_8',
    'font_family': 'Sim<PERSON><PERSON>',  # 中文字体
    'font_size': 12
}

# 数据验证配置
DATA_VALIDATION = {
    'expected_sectors': 86,  # 预期板块数量
    'required_columns': [
        'sector_code', 'sector_name', 'date', 
        'open', 'close', 'high', 'low', 
        'volume', 'amount', 'amplitude', 
        'change_pct', 'change_amount', 'turnover_rate'
    ]
}

# 性能配置
PERFORMANCE_CONFIG = {
    'chunk_size': 100,  # 数据分块大小
    'max_workers': 4,   # 最大并行工作进程数
    'memory_limit_mb': 2048  # 内存使用限制（MB）
}

# 输出格式配置
OUTPUT_CONFIG = {
    'console_width': 120,
    'decimal_places': 2,
    'percentage_format': '{:.2f}%',
    'date_format': '%Y-%m-%d'
}
