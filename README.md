# A股板块数据分析程序

## 项目简介

本程序基于2020年至2025年7月22日的A股板块真实数据，实现三大核心分析功能：

1. **时间窗口累计涨跌幅分析**：计算指定时间窗口内各板块的累计涨跌幅表现
2. **板块排名频次统计**：统计各板块进入前10名的频次
3. **单日冠军板块统计**：统计获得单日涨跌幅第一名次数最多的板块

## 数据说明

- **数据时间范围**：2020年5月18日 - 2025年7月22日
- **数据内容**：每个A股交易日的86个板块数据
- **数据字段**：板块代码、板块名称、日期、开盘价、收盘价、最高价、最低价、成交量、成交额、振幅、涨跌幅、涨跌额、换手率
- **数据文件**：约1200+个CSV文件，存储在`data/`目录

## 项目结构

```
gupiao_bk_fenxinew/
├── data/                   # 数据文件目录
├── src/                    # 源代码目录
│   ├── data_loader.py      # 数据加载模块
│   ├── time_window_analyzer.py  # 时间窗口分析模块
│   ├── ranking_analyzer.py     # 排名统计模块
│   ├── visualizer.py           # 可视化模块
│   └── utils.py               # 工具函数模块
├── output/                 # 输出文件目录
├── main.py                 # 主程序入口
├── config.py               # 配置文件
├── requirements.txt        # 依赖包配置
└── README.md              # 项目说明
```

## 安装和使用

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行程序

```bash
# 使用默认参数运行
python main.py

# 指定时间窗口
python main.py --windows 7,14,30

# 指定结束日期
python main.py --end-date 2025-07-22

# 不生成图表
python main.py --no-charts

# 详细输出模式
python main.py --verbose
```

### 3. 命令行参数

- `--windows`：时间窗口（天数），用逗号分隔，默认：7,14,30
- `--end-date`：分析结束日期，格式：YYYY-MM-DD，默认：最新数据日期
- `--output-dir`：输出目录，默认：output
- `--no-charts`：禁用图表生成
- `--verbose`：详细输出模式
- `--top-n`：排行榜显示数量，默认：10

## 输出结果

程序将生成以下输出：

1. **控制台输出**：格式化的分析结果表格
2. **CSV文件**：详细的数据导出
3. **图表文件**：可视化分析图表（PNG格式）
4. **HTML报告**：综合分析报告

## 技术特性

- **高性能**：采用pandas和numpy进行向量化计算
- **内存优化**：分块加载大数据集，避免内存溢出
- **可视化**：使用matplotlib和seaborn生成专业图表
- **用户友好**：进度条显示、详细错误信息、美化输出
- **可扩展**：模块化设计，便于功能扩展

## 开发状态

- [x] 项目结构初始化
- [ ] 数据加载模块
- [ ] 时间窗口分析模块
- [ ] 排名统计模块
- [ ] 可视化模块
- [ ] 主程序集成
- [ ] 输出格式优化
- [ ] 测试和文档完善

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请联系开发者。
