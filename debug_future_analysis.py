#!/usr/bin/env python3
"""
调试未来时间窗口分析问题
"""

import sys
import os
import traceback

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_main_execution():
    """调试main.py的执行流程"""
    print("🔍 调试main.py执行流程")
    print("=" * 60)
    
    try:
        # 清除模块缓存
        modules_to_clear = ['main', 'config', 'src.time_window_analyzer']
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        # 导入模块
        import config
        from main import parse_arguments, validate_arguments
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        
        print("✅ 模块导入成功")
        
        # 检查配置
        print(f"\n📋 配置检查:")
        print(f"   TIME_WINDOWS: {config.TIME_WINDOWS}")
        print(f"   FUTURE_TIME_WINDOWS: {config.FUTURE_TIME_WINDOWS}")
        print(f"   DATA_YEAR_LIMIT: {config.DATA_YEAR_LIMIT}")
        
        # 模拟参数解析
        original_argv = sys.argv.copy()
        sys.argv = ['main.py']
        
        try:
            args = parse_arguments()
            args = validate_arguments(args)
            
            print(f"\n🔧 参数验证结果:")
            print(f"   args.windows: {args.windows}")
            print(f"   args.future_windows: {args.future_windows}")
            print(f"   args.enable_future_analysis: {args.enable_future_analysis}")
            
            # 加载少量数据进行测试
            print(f"\n📊 数据加载测试:")
            loader = DataLoader()
            loader.file_list = loader.file_list[:5]  # 只加载5个文件
            data = loader.load_all_data()
            print(f"   数据形状: {data.shape}")
            
            # 初始化分析器
            print(f"\n🔧 分析器初始化:")
            analyzer = TimeWindowAnalyzer(data)
            print(f"   全部数据日期范围: {analyzer.date_range}")
            print(f"   过滤数据日期范围: {analyzer.filtered_date_range}")
            print(f"   全部交易日数: {len(analyzer.available_dates)}")
            print(f"   过滤交易日数: {len(analyzer.filtered_available_dates)}")
            
            # 检查方法是否存在
            print(f"\n🔍 方法检查:")
            methods = [
                'calculate_window_performance',
                'calculate_future_window_performance',
                'calculate_multiple_future_windows',
                '_get_future_trading_days_in_window',
                '_filter_data_for_future_analysis'
            ]
            
            for method_name in methods:
                if hasattr(analyzer, method_name):
                    print(f"   ✅ {method_name}")
                else:
                    print(f"   ❌ {method_name} - 方法不存在！")
            
            # 测试分析日期
            if not analyzer.available_dates:
                print(f"\n❌ 没有可用的分析日期")
                return False
                
            end_date = analyzer.available_dates[-1]
            print(f"\n📅 测试分析日期: {end_date}")
            
            # 测试历史分析
            print(f"\n📈 历史时间窗口分析测试:")
            window_results = {}
            for window_days in args.windows:
                try:
                    performance = analyzer.calculate_window_performance(end_date, window_days)
                    if not performance.empty:
                        window_results[window_days] = performance
                        top_sector = performance.iloc[0]
                        print(f"   历史{window_days:2d}日窗口: {len(performance)} 个板块, 最佳: {top_sector['sector_name']} ({top_sector['cumulative_return_pct']:.2f}%)")
                    else:
                        print(f"   历史{window_days:2d}日窗口: 无数据")
                except Exception as e:
                    print(f"   历史{window_days:2d}日窗口: 错误 - {str(e)}")
                    traceback.print_exc()
            
            # 测试未来分析
            print(f"\n🔮 未来时间窗口分析测试:")
            future_window_results = {}
            
            # 检查是否有过滤后的数据
            if not analyzer.filtered_available_dates:
                print(f"   ❌ 没有过滤后的数据用于未来分析")
                print(f"   原因: 可能所有数据都在2024年之后")
                return False
            
            # 选择一个较早的日期进行测试
            test_date = None
            for date in analyzer.filtered_available_dates:
                if date.year <= 2023:  # 选择2023年或更早的日期
                    test_date = date
                    break
            
            if not test_date:
                test_date = analyzer.filtered_available_dates[-50] if len(analyzer.filtered_available_dates) >= 50 else analyzer.filtered_available_dates[0]
            
            print(f"   使用测试日期: {test_date}")
            
            for window_days in args.future_windows:
                try:
                    performance = analyzer.calculate_future_window_performance(test_date, window_days)
                    if not performance.empty:
                        future_window_results[window_days] = performance
                        top_sector = performance.iloc[0]
                        print(f"   未来{window_days:2d}日窗口: {len(performance)} 个板块, 最佳: {top_sector['sector_name']} ({top_sector['cumulative_return_pct']:.2f}%)")
                    else:
                        print(f"   未来{window_days:2d}日窗口: 无数据")
                except Exception as e:
                    print(f"   未来{window_days:2d}日窗口: 错误 - {str(e)}")
                    traceback.print_exc()
            
            # 统计结果
            total_windows = len(window_results) + len(future_window_results)
            print(f"\n📊 分析结果统计:")
            print(f"   历史窗口: {len(window_results)} 个")
            print(f"   未来窗口: {len(future_window_results)} 个")
            print(f"   总计: {total_windows} 个时间窗口")
            
            if total_windows == 6:
                print(f"   ✅ 预期结果正确（6个时间窗口）")
            else:
                print(f"   ❌ 结果不符合预期（应该是6个时间窗口）")
            
            return total_windows == 6
            
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        traceback.print_exc()
        return False

def debug_date_issue():
    """调试日期问题"""
    print(f"\n🗓️ 调试日期问题")
    print("-" * 40)
    
    try:
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        import config
        
        # 加载数据
        loader = DataLoader()
        loader.file_list = loader.file_list[:3]
        data = loader.load_all_data()
        
        analyzer = TimeWindowAnalyzer(data)
        
        print(f"全部数据日期范围: {analyzer.date_range}")
        print(f"过滤数据日期范围: {analyzer.filtered_date_range}")
        
        # 检查2025年的数据
        dates_2025 = [d for d in analyzer.available_dates if d.year == 2025]
        dates_2024 = [d for d in analyzer.available_dates if d.year == 2024]
        dates_2023 = [d for d in analyzer.available_dates if d.year == 2023]
        
        print(f"2025年数据: {len(dates_2025)} 天")
        print(f"2024年数据: {len(dates_2024)} 天")
        print(f"2023年数据: {len(dates_2023)} 天")
        
        # 检查过滤后的数据
        filtered_dates_2024 = [d for d in analyzer.filtered_available_dates if d.year == 2024]
        filtered_dates_2023 = [d for d in analyzer.filtered_available_dates if d.year == 2023]
        
        print(f"过滤后2024年数据: {len(filtered_dates_2024)} 天")
        print(f"过滤后2023年数据: {len(filtered_dates_2023)} 天")
        
        # 如果使用2025年的日期作为分析日期，可能导致未来分析无数据
        if analyzer.available_dates[-1].year == 2025:
            print(f"⚠️ 问题发现：最新日期是2025年，但未来分析只使用≤2024年的数据")
            print(f"   这意味着从2025年的日期无法找到足够的未来数据")
            
            # 建议使用2024年的日期
            dates_2024_sorted = sorted([d for d in analyzer.filtered_available_dates if d.year == 2024])
            if dates_2024_sorted:
                suggested_date = dates_2024_sorted[-1]  # 2024年的最后一个日期
                print(f"   建议使用日期: {suggested_date}")
                
                # 测试这个日期的未来分析
                print(f"\n测试建议日期的未来分析:")
                for window_days in config.FUTURE_TIME_WINDOWS:
                    try:
                        result = analyzer.calculate_future_window_performance(suggested_date, window_days)
                        print(f"   未来{window_days}日: {len(result)} 个板块")
                    except Exception as e:
                        print(f"   未来{window_days}日: 错误 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日期调试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始调试未来时间窗口分析问题")
    print("=" * 60)
    
    # 调试main.py执行流程
    success1 = debug_main_execution()
    
    # 调试日期问题
    success2 = debug_date_issue()
    
    if success1 and success2:
        print("\n🎉 调试完成，找到了问题原因")
    else:
        print("\n💥 调试发现问题，需要修复")
    
    return success1 and success2

if __name__ == "__main__":
    main()
