<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史-未来板块一致性分析演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #34495e;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        .analysis-note {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .consistency-note {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .rank-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 12px;
            min-width: 20px;
            text-align: center;
        }
        .rank-badge.rank-1 {
            background-color: #ffd700;
            color: #333;
        }
        .rank-badge.rank-2 {
            background-color: #c0c0c0;
            color: #333;
        }
        .rank-badge.rank-3 {
            background-color: #cd7f32;
            color: white;
        }
        .rank-badge.rank-other {
            background-color: #6c757d;
            color: white;
        }
        .consistency-rate.excellent {
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .consistency-rate.good {
            background-color: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .consistency-rate.fair {
            background-color: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .sector-name {
            font-weight: 500;
        }
        .years-list {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>历史-未来板块一致性分析演示</h1>
            <div class="subtitle">功能实现演示 - 2025-07-24</div>
        </div>

        <div class="section">
            <h2>🔄 历史-未来板块一致性分析</h2>
            <div class="analysis-note">
                <p><strong>📝 分析说明：</strong>识别在历史时间窗口中表现优秀的板块，与未来时间窗口中出现的相同板块，进行交叉对比分析。</p>
                <ul>
                    <li><strong>历史优秀板块：</strong>各历史时间窗口的表现最佳板块、单日冠军次数前10名、频次排行榜前10名</li>
                    <li><strong>未来预测板块：</strong>各未来时间窗口的收益率排行榜前10名板块</li>
                    <li><strong>重合度评分：</strong>基于板块在多个时间窗口中的出现频次和排名进行加权计算</li>
                </ul>
            </div>
            
            <div class="consistency-note">
                <p><strong>📊 统计概览：</strong></p>
                <ul>
                    <li>历史优秀板块总数：15 个</li>
                    <li>未来预测板块总数：12 个</li>
                    <li>重合板块总数：8 个</li>
                    <li>重合率：53.3%</li>
                </ul>
            </div>
            
            <h3>🏆 重合度排行榜（前10名）</h3>
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>板块名称</th>
                        <th>重合度评分</th>
                        <th>历史得分</th>
                        <th>未来得分</th>
                        <th>历史窗口数</th>
                        <th>未来窗口数</th>
                        <th>详细信息</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="rank-badge rank-1">1</span></td>
                        <td class="sector-name">水泥建材</td>
                        <td><span class="consistency-rate excellent">68.5</span></td>
                        <td>45</td>
                        <td>28</td>
                        <td>3</td>
                        <td>3</td>
                        <td class="years-list">历史: 7日第1名(19.1%), 14日第1名(15.8%), 冠军第1名(8次) | 未来: 30日第1名(8.5%), 60日第1名(7.2%)</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge rank-2">2</span></td>
                        <td class="sector-name">钢铁行业</td>
                        <td><span class="consistency-rate good">45.2</span></td>
                        <td>32</td>
                        <td>18</td>
                        <td>3</td>
                        <td>3</td>
                        <td class="years-list">历史: 7日第2名(17.4%), 14日第3名(12.4%), 冠军第2名(6次) | 未来: 30日第2名(6.2%), 60日第4名(5.1%)</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge rank-3">3</span></td>
                        <td class="sector-name">工程机械</td>
                        <td><span class="consistency-rate good">42.8</span></td>
                        <td>28</td>
                        <td>22</td>
                        <td>2</td>
                        <td>2</td>
                        <td class="years-list">历史: 7日第3名(16.9%), 频次第2名(22次) | 未来: 60日第2名(6.8%), 90日第1名(6.5%)</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge rank-other">4</span></td>
                        <td class="sector-name">玻璃玻纤</td>
                        <td><span class="consistency-rate fair">28.6</span></td>
                        <td>18</td>
                        <td>15</td>
                        <td>2</td>
                        <td>2</td>
                        <td class="years-list">历史: 7日第4名(15.6%), 30日第4名(15.6%) | 未来: 30日第3名(5.8%), 90日第4名(5.4%)</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge rank-other">5</span></td>
                        <td class="sector-name">医疗服务</td>
                        <td><span class="consistency-rate fair">25.4</span></td>
                        <td>15</td>
                        <td>12</td>
                        <td>2</td>
                        <td>2</td>
                        <td class="years-list">历史: 14日第4名(10.3%), 频次第3名(18次) | 未来: 60日第3名(5.5%), 90日第5名(5.0%)</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="analysis-note">
                <p><strong>📝 重合度评分说明：</strong></p>
                <ul>
                    <li><strong>历史得分：</strong>基于板块在历史时间窗口表现、冠军次数、频次排行榜中的排名计算</li>
                    <li><strong>未来得分：</strong>基于板块在未来时间窗口收益率排行榜中的排名计算</li>
                    <li><strong>重合度评分：</strong>(历史得分 + 未来得分) × 时间窗口覆盖度</li>
                    <li><strong>评分等级：</strong>≥50分为优秀，30-49分为良好，<30分为一般</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
