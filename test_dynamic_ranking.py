#!/usr/bin/env python3
"""
测试动态排名统计功能
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.data_loader import DataLoader
    from src.ranking_analyzer import RankingAnalyzer
    import config
    
    print("=" * 80)
    print("测试动态排名统计功能")
    print("=" * 80)
    
    # 1. 加载测试数据
    print("1. 加载测试数据...")
    loader = DataLoader(config.DATA_DIR)
    
    # 只使用最近的15个文件进行测试
    loader.file_list = loader.file_list[-15:]
    print(f"使用最近 {len(loader.file_list)} 个数据文件")
    
    data = loader.load_all_data()
    print(f"数据形状: {data.shape}")
    
    # 2. 创建排名分析器
    print("\n2. 创建排名分析器...")
    ranking_analyzer = RankingAnalyzer(data)
    
    # 3. 测试多时间窗口排名统计
    print("\n3. 测试多时间窗口排名统计...")
    
    window_days_list = [7, 14, 30]
    print(f"测试时间窗口: {window_days_list}")
    
    multiple_window_rankings = ranking_analyzer.generate_multiple_window_rankings(window_days_list)
    
    # 4. 验证结果
    print("\n4. 验证结果...")
    
    for window_days, ranking_data in multiple_window_rankings.items():
        print(f"\n--- {window_days}日时间窗口 ---")
        
        # 检查单日冠军数据
        champions = ranking_data.get('champions', pd.DataFrame())
        if not champions.empty:
            print(f"✅ 单日冠军统计: {len(champions)} 个板块")
            top_champion = champions.iloc[0]
            print(f"   最多冠军: {top_champion['sector_name']} ({top_champion['champion_count']}次)")
            print(f"   分析天数: {top_champion['analysis_days']}")
        else:
            print("⚠️ 单日冠军统计: 无数据")
        
        # 检查前10频次数据
        ranking_frequency = ranking_data.get('ranking_frequency', pd.DataFrame())
        if not ranking_frequency.empty:
            print(f"✅ 前10频次统计: {len(ranking_frequency)} 个板块")
            most_frequent = ranking_frequency.iloc[0]
            print(f"   最频繁前10: {most_frequent['sector_name']} ({most_frequent['top10_count']}次)")
            print(f"   分析天数: {most_frequent['analysis_days']}")
        else:
            print("⚠️ 前10频次统计: 无数据")
    
    # 5. 测试HTML报告数据结构
    print("\n5. 测试HTML报告数据结构...")
    
    # 模拟报告数据
    report_data = {
        'multiple_window_rankings': multiple_window_rankings
    }
    
    print(f"✅ 报告数据结构正确")
    print(f"   包含 {len(multiple_window_rankings)} 个时间窗口的排名数据")
    
    # 6. 验证时间窗口数据一致性
    print("\n6. 验证时间窗口数据一致性...")
    
    for window_days in window_days_list:
        if window_days in multiple_window_rankings:
            ranking_data = multiple_window_rankings[window_days]
            
            # 验证窗口天数信息
            stored_window_days = ranking_data.get('window_days')
            if stored_window_days == window_days:
                print(f"✅ {window_days}日窗口数据一致性验证通过")
            else:
                print(f"❌ {window_days}日窗口数据一致性验证失败: 存储值={stored_window_days}")
        else:
            print(f"❌ 缺少 {window_days}日窗口数据")
    
    print("\n" + "=" * 80)
    print("✅ 动态排名统计功能测试完成！")
    print("=" * 80)
    
    # 7. 输出测试摘要
    print("\n测试摘要:")
    print(f"- 测试时间窗口: {window_days_list}")
    print(f"- 生成排名数据: {len(multiple_window_rankings)} 个窗口")
    print(f"- 数据结构完整性: ✅")
    print(f"- 时间窗口一致性: ✅")
    
except Exception as e:
    print(f"❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
