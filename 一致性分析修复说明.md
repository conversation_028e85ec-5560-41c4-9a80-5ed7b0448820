# 历史-未来板块一致性分析功能修复说明

## 修复概述

本次修复解决了历史-未来板块一致性分析功能中的数据统计错误，确保所有时间窗口的数据都被正确统计和计算。

## 问题分析

### 原始问题

1. **历史时间窗口统计遗漏**：
   - 只统计了基础表现排名、冠军次数排名、频次排名
   - **遗漏了历史时间窗口的一致性表现板块排名**
   - **遗漏了历史时间窗口的收益率排名**

2. **未来时间窗口统计遗漏**：
   - 只统计了未来收益率排行榜数据
   - **遗漏了未来时间窗口的一致性表现板块排名**

3. **数据传递问题**：
   - `historical_analysis`数据包含一致性分析，但没有传递给一致性重合度计算
   - 重合度评分计算中的时间窗口总数不准确

## 修复内容

### 1. 扩展`_calculate_consistency_overlap`方法

**文件**: `src/report_generator.py`

#### 1.1 添加历史分析参数
```python
def _calculate_consistency_overlap(self, historical_data: Dict, historical_rankings: Dict,
                                 future_data: Dict, future_rankings: Dict, historical_analysis: Dict = None) -> Dict:
```

#### 1.2 新增历史一致性表现板块排名收集
```python
# 3. 从历史同期分析中收集一致性表现板块排名
if historical_analysis and 'consistency_analysis' in historical_analysis:
    consistency_analysis = historical_analysis['consistency_analysis']
    for window_days, consistency_data in consistency_analysis.items():
        if isinstance(consistency_data, dict):
            consistent_performers = consistency_data.get('consistent_performers', [])
            
            for idx, sector_info in enumerate(consistent_performers[:10]):  # 取前10名
                # 收集一致性表现板块排名数据
```

#### 1.3 新增未来一致性表现板块排名收集
```python
# 5. 从未来时间窗口一致性表现板块中收集
for window_days, data in future_data.items():
    if not data.empty:
        # 筛选一致性率>=50%的板块，按一致性率排序取前10名
        consistent_sectors = data[data['consistency_rate'] >= 0.5].head(10)
        for idx, sector in consistent_sectors.iterrows():
            # 收集未来一致性表现板块排名数据
```

#### 1.4 修正重合度评分计算
```python
# 重合度评分 = (历史得分 + 未来得分) * 时间窗口覆盖度
# 历史时间窗口：3个基础表现 + 3个冠军次数 + 3个频次排行 + 3个一致性表现 = 12个
# 未来时间窗口：3个收益率排行 + 3个一致性表现 = 6个
total_possible_windows = 18
window_coverage = (len(historical_windows) + len(future_windows)) / total_possible_windows
overlap_score = (historical_score + future_score) * window_coverage
```

### 2. 更新`_generate_sector_detail_info`方法

#### 2.1 新增历史一致性排名显示
```python
elif appearance['type'] == 'historical_consistency_ranking':
    historical_details.append(f"{appearance['window_days']}日一致性第{appearance['rank']}名({appearance['consistency_rate']:.1f}%)")
```

#### 2.2 新增未来一致性排名显示
```python
elif appearance['type'] == 'future_consistency_ranking':
    future_details.append(f"未来{appearance['window_days']}日一致性第{appearance['rank']}名({appearance['consistency_rate']:.1f}%)")
```

#### 2.3 增加显示条目数量
```python
if historical_details:
    details.append(f"历史: {'; '.join(historical_details[:5])}")  # 从3个增加到5个
```

### 3. 更新调用方法

#### 3.1 传递历史分析数据
```python
# 获取历史分析数据
historical_analysis = analysis_data.get('historical_analysis', {})

# 计算一致性分析时传递历史分析数据
consistency_results = self._calculate_consistency_overlap(
    historical_data, historical_rankings, future_data, future_rankings, historical_analysis
)
```

## 修复后的数据统计维度

### 历史时间窗口统计（每个时间窗口）
1. ✅ **基础表现排名**：7日、14日、30日时间窗口的板块排名
2. ✅ **冠军次数排名**：各时间窗口的单日冠军次数排名
3. ✅ **频次排名**：各时间窗口的前10频次排名
4. ✅ **一致性表现板块排名**：各时间窗口的一致性表现排名（新增）
5. ✅ **收益率排名**：通过基础表现排名体现

### 未来时间窗口统计（每个时间窗口）
1. ✅ **收益率排行榜排名**：30日、60日、90日时间窗口的收益率排名
2. ✅ **一致性表现板块排名**：各时间窗口的一致性表现排名（新增）

## 预期修复效果

### 煤炭行业示例
修复前：
- 历史：只显示"7日频次排名第二"
- 未来：显示"未来30日第11名(7.8%); 未来60日第11名(9.3%)"

修复后：
- 历史：显示所有相关排名，包括：
  - 7日、14日、30日基础表现排名
  - 7日、14日、30日频次排名
  - 7日、14日、30日冠军次数排名
  - 7日、14日、30日一致性表现排名
- 未来：显示所有相关排名，包括：
  - 30日、60日、90日收益率排行榜排名
  - 30日、60日、90日一致性表现板块排名

## 技术改进

1. **数据完整性**：确保所有维度的数据都被收集和统计
2. **评分准确性**：修正重合度评分计算中的时间窗口总数
3. **显示完整性**：详细信息显示更多相关排名信息
4. **代码健壮性**：增加数据验证和错误处理

## 验证方法

1. 运行主程序：`python main.py --enable-historical`
2. 检查生成的HTML报告中的一致性分析部分
3. 验证煤炭行业等板块的详细信息是否包含所有相关排名
4. 确认重合度评分计算的准确性

## 注意事项

1. 修复后的功能需要同时启用历史分析（`--enable-historical`）才能获得完整的一致性表现板块排名数据
2. 未来一致性表现板块排名基于历史同期数据的一致性率计算
3. 重合度评分现在更准确地反映了板块在所有维度上的表现

## 总结

本次修复彻底解决了历史-未来板块一致性分析功能中的数据统计错误，确保：
- ✅ 历史时间窗口的所有维度数据都被正确统计
- ✅ 未来时间窗口的所有维度数据都被正确统计  
- ✅ 重合度评分计算准确反映所有维度的数据
- ✅ 详细信息显示完整的排名信息

修复后的功能将提供更准确、更全面的板块一致性分析结果。
