#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化测试脚本：验证一致性分析修复
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_import():
    """测试模块导入"""
    try:
        from report_generator import ReportGenerator
        print("✅ 模块导入成功")
        
        # 创建实例
        generator = ReportGenerator("output")
        print("✅ ReportGenerator实例创建成功")
        
        # 测试方法存在
        if hasattr(generator, '_calculate_consistency_overlap'):
            print("✅ _calculate_consistency_overlap方法存在")
        else:
            print("❌ _calculate_consistency_overlap方法不存在")
            
        if hasattr(generator, '_generate_sector_detail_info'):
            print("✅ _generate_sector_detail_info方法存在")
        else:
            print("❌ _generate_sector_detail_info方法不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_method_signature():
    """测试方法签名"""
    try:
        from report_generator import ReportGenerator
        import inspect
        
        generator = ReportGenerator("output")
        
        # 检查_calculate_consistency_overlap方法签名
        method = getattr(generator, '_calculate_consistency_overlap')
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        
        print(f"📝 _calculate_consistency_overlap参数: {params}")
        
        # 检查是否包含historical_analysis参数
        if 'historical_analysis' in params:
            print("✅ historical_analysis参数已添加")
        else:
            print("❌ historical_analysis参数未添加")
            
        return True
        
    except Exception as e:
        print(f"❌ 方法签名检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 简化测试：验证一致性分析修复")
    print("=" * 50)
    
    # 测试1：模块导入
    print("\n📦 测试1：模块导入")
    if not test_import():
        return False
    
    # 测试2：方法签名
    print("\n📝 测试2：方法签名")
    if not test_method_signature():
        return False
    
    print("\n✅ 所有测试通过！修复代码语法正确")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 修复验证成功！")
    else:
        print("\n❌ 修复验证失败！")
