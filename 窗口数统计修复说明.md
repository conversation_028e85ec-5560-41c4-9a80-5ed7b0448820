# 历史-未来板块一致性分析表格数据统计修复说明

## 问题分析

### 发现的问题

在一致性分析表格中，存在**表格统计数据与详细信息不匹配**的严重问题：

**问题表现**：
- **表格显示**：煤炭行业历史窗口数=2，未来窗口数=3
- **详细信息显示**：历史相关项目至少4个，未来相关项目至少5个

### 根本原因

**错误的统计逻辑**：
```python
# 原始错误代码
historical_windows = set()
future_windows = set()

for appearance in historical_info['appearances']:
    historical_windows.add(appearance['window_days'])  # 只统计不同的天数

for appearance in future_info['appearances']:
    future_windows.add(appearance['window_days'])      # 只统计不同的天数

# 结果：只统计去重后的时间窗口天数（如7日、14日、30日）
# 但忽略了每个时间窗口可能有多种排名类型
```

**问题分析**：
1. 使用`set()`去重，只统计不同的`window_days`值
2. 忽略了同一时间窗口内的多种排名类型（基础表现、冠军次数、频次、一致性等）
3. 导致表格显示的窗口数远少于详细信息中的实际项目数

## 修复方案

### 修复逻辑

**正确的统计方式**：
```python
# 修复后的正确代码
# 计算出现的项目数量（每个appearance都是一个独立的项目）
historical_appearances_count = len(historical_info['appearances'])
future_appearances_count = len(future_info['appearances'])
```

**修复原理**：
- 统计**所有出现的项目数量**，而不是去重的时间窗口天数
- 每个`appearance`代表一个独立的排名项目
- 确保表格统计与详细信息显示完全一致

### 具体修改

#### 1. 修改窗口数统计逻辑

**文件**: `src/report_generator.py`

**修改位置**: `_calculate_consistency_overlap`方法

```python
# 修改前（错误）
historical_windows = set()
future_windows = set()
for appearance in historical_info['appearances']:
    historical_windows.add(appearance['window_days'])
for appearance in future_info['appearances']:
    future_windows.add(appearance['window_days'])

# 修改后（正确）
historical_appearances_count = len(historical_info['appearances'])
future_appearances_count = len(future_info['appearances'])
```

#### 2. 修改重合度评分计算

```python
# 修改前
window_coverage = (len(historical_windows) + len(future_windows)) / total_possible_windows
overlap_score = (historical_score + future_score) * window_coverage

# 修改后
appearance_coverage = (historical_appearances_count + future_appearances_count) / total_possible_appearances
overlap_score = (historical_score + future_score) * appearance_coverage
```

#### 3. 修改结果数据结构

```python
# 修改前
'historical_windows': len(historical_windows),
'future_windows': len(future_windows),

# 修改后
'historical_windows': historical_appearances_count,  # 使用项目数量
'future_windows': future_appearances_count,          # 使用项目数量
```

#### 4. 更新表格列标题

```python
# 修改前
'<th>历史窗口数</th>'
'<th>未来窗口数</th>'

# 修改后
'<th>历史项目数</th>'
'<th>未来项目数</th>'
```

## 修复效果

### 以煤炭行业为例

**修复前（错误）**：
- 表格显示：历史窗口数=2，未来窗口数=3
- 详细信息：历史4项，未来5项
- 问题：数据不一致，用户困惑

**修复后（正确）**：
- 表格显示：历史项目数=4，未来项目数=5
- 详细信息：历史4项，未来5项
- 结果：数据完全一致，逻辑清晰

### 数据一致性验证

**历史项目统计**：
1. 历史7日基础表现排名
2. 7日冠军次数排名
3. 7日频次排名
4. 7日一致性表现排名
5. 历史14日基础表现排名
6. 14日频次排名
**总计**：6个项目

**未来项目统计**：
1. 未来30日收益率排名
2. 未来30日一致性排名
3. 未来60日收益率排名
4. 未来60日一致性排名
5. 未来90日收益率排名
**总计**：5个项目

## 技术改进

### 1. 统计准确性
- ✅ 表格数据与详细信息完全一致
- ✅ 每个排名项目都被正确统计
- ✅ 消除了用户对数据不一致的困惑

### 2. 语义清晰性
- ✅ 列标题从"窗口数"改为"项目数"，更准确反映统计内容
- ✅ 重合度评分基于实际项目数量计算
- ✅ 覆盖度计算更加精确

### 3. 代码健壮性
- ✅ 简化了统计逻辑，减少出错可能
- ✅ 直接使用列表长度，避免复杂的集合操作
- ✅ 提高了代码的可读性和维护性

## 验证方法

### 1. 数据一致性检查
```python
# 验证表格数据与详细信息的一致性
assert len(sector['historical_appearances']) == sector_result['historical_windows']
assert len(sector['future_appearances']) == sector_result['future_windows']
```

### 2. 重合度评分验证
```python
# 验证评分计算的准确性
expected_coverage = (hist_count + future_count) / 18
expected_score = (hist_score + future_score) * expected_coverage
assert abs(actual_score - expected_score) < 0.01
```

## 总结

本次修复解决了历史-未来板块一致性分析表格中的核心问题：

1. **问题根源**：错误的窗口数统计逻辑导致表格数据与详细信息不一致
2. **修复方案**：改为统计实际项目数量，确保数据一致性
3. **修复效果**：表格显示的项目数与详细信息中的项目数完全匹配
4. **用户体验**：消除了数据不一致带来的困惑，提高了分析结果的可信度

修复后的功能将为用户提供准确、一致的板块一致性分析数据。
