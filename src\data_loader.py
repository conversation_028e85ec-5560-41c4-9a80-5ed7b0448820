"""
数据加载模块
负责批量读取CSV文件，数据验证和预处理
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, Tuple, List, Dict
import logging
import os
import sys
from datetime import datetime
from tqdm import tqdm
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

# 忽略pandas的警告
warnings.filterwarnings('ignore', category=pd.errors.DtypeWarning)

class DataLoader:
    """数据加载器类"""

    def __init__(self, data_dir: str = None):
        """
        初始化数据加载器

        Args:
            data_dir: 数据目录路径，默认使用config中的配置
        """
        self.data_dir = Path(data_dir or config.DATA_DIR)
        self.logger = self._setup_logger()
        self.data_cache = None
        self.file_list = []

        # 验证数据目录存在
        if not self.data_dir.exists():
            raise FileNotFoundError(f"数据目录不存在: {self.data_dir}")

        # 获取所有CSV文件
        self.file_list = sorted(list(self.data_dir.glob("*.csv")))
        if not self.file_list:
            raise FileNotFoundError(f"数据目录中没有找到CSV文件: {self.data_dir}")

        self.logger.info(f"找到 {len(self.file_list)} 个数据文件")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _load_single_file(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        加载单个CSV文件

        Args:
            file_path: CSV文件路径

        Returns:
            DataFrame或None（如果加载失败）
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8')

            # 基本数据验证
            if df.empty:
                self.logger.warning(f"文件为空: {file_path.name}")
                return None

            # 检查必需的列
            required_columns = config.DATA_VALIDATION['required_columns']
            missing_columns = set(required_columns) - set(df.columns)
            if missing_columns:
                self.logger.error(f"文件 {file_path.name} 缺少必需列: {missing_columns}")
                return None

            # 数据类型优化
            df = self._optimize_dtypes(df)

            # 确保日期列格式正确
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'], errors='coerce')

            return df

        except Exception as e:
            self.logger.error(f"加载文件失败 {file_path.name}: {str(e)}")
            return None

    def _optimize_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        优化数据类型以减少内存使用

        Args:
            df: 原始DataFrame

        Returns:
            优化后的DataFrame
        """
        # 数值列优化
        numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount',
                          'amplitude', 'change_pct', 'change_amount', 'turnover_rate']

        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                # 根据数值范围选择合适的数据类型
                if df[col].dtype == 'float64':
                    df[col] = df[col].astype('float32')

        # 字符串列优化
        string_columns = ['sector_code', 'sector_name']
        for col in string_columns:
            if col in df.columns:
                df[col] = df[col].astype('category')

        return df

    def load_all_data(self, use_cache: bool = True) -> pd.DataFrame:
        """
        加载所有数据文件

        Args:
            use_cache: 是否使用缓存

        Returns:
            合并后的DataFrame
        """
        if use_cache and self.data_cache is not None:
            self.logger.info("使用缓存数据")
            return self.data_cache

        self.logger.info("开始加载所有数据文件...")
        all_dataframes = []
        failed_files = []

        # 使用进度条显示加载进度
        for file_path in tqdm(self.file_list, desc="加载数据文件", unit="文件"):
            df = self._load_single_file(file_path)
            if df is not None:
                all_dataframes.append(df)
            else:
                failed_files.append(file_path.name)

        if not all_dataframes:
            raise ValueError("没有成功加载任何数据文件")

        # 合并所有数据
        self.logger.info(f"合并 {len(all_dataframes)} 个数据文件...")
        combined_df = pd.concat(all_dataframes, ignore_index=True)

        # 设置多级索引
        combined_df = combined_df.set_index(['date', 'sector_code']).sort_index()

        # 缓存数据
        self.data_cache = combined_df

        # 报告加载结果
        self.logger.info(f"数据加载完成:")
        self.logger.info(f"  - 成功加载: {len(all_dataframes)} 个文件")
        self.logger.info(f"  - 失败文件: {len(failed_files)} 个")
        self.logger.info(f"  - 总记录数: {len(combined_df):,}")
        self.logger.info(f"  - 内存使用: {combined_df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")

        if failed_files:
            self.logger.warning(f"失败的文件: {failed_files}")

        return combined_df

    def validate_data(self, df: pd.DataFrame) -> bool:
        """
        验证数据完整性

        Args:
            df: 要验证的DataFrame

        Returns:
            验证是否通过
        """
        try:
            self.logger.info("开始数据验证...")

            # 检查数据是否为空
            if df.empty:
                self.logger.error("数据为空")
                return False

            # 检查必需的列（考虑多级索引的情况）
            required_columns = config.DATA_VALIDATION['required_columns']

            # 获取所有可用的列名（包括索引）
            available_columns = set(df.columns)
            if isinstance(df.index, pd.MultiIndex):
                # 如果是多级索引，添加索引名称
                available_columns.update(df.index.names)
            elif df.index.name:
                # 如果是单级索引且有名称
                available_columns.add(df.index.name)

            missing_columns = set(required_columns) - available_columns
            if missing_columns:
                self.logger.error(f"缺少必需列: {missing_columns}")
                self.logger.debug(f"可用列: {available_columns}")
                return False

            # 检查数据类型
            if 'change_pct' not in df.columns:
                self.logger.error("缺少关键字段: change_pct")
                return False

            # 检查日期范围
            if isinstance(df.index, pd.MultiIndex) and 'date' in df.index.names:
                dates = df.index.get_level_values('date')
                date_range = (dates.min(), dates.max())
                self.logger.info(f"数据日期范围: {date_range[0]} 到 {date_range[1]}")

            # 检查板块数量变化
            if isinstance(df.index, pd.MultiIndex) and 'sector_code' in df.index.names:
                sector_counts = df.groupby(level='date').size()
                min_sectors = sector_counts.min()
                max_sectors = sector_counts.max()
                self.logger.info(f"板块数量范围: {min_sectors} 到 {max_sectors}")

                # 检查是否有异常的板块数量
                if min_sectors < 50:  # 假设最少应该有50个板块
                    self.logger.warning(f"某些日期的板块数量过少: {min_sectors}")

            # 检查关键数值字段的有效性
            numeric_fields = ['change_pct', 'close', 'volume']
            for field in numeric_fields:
                if field in df.columns:
                    null_count = df[field].isnull().sum()
                    if null_count > 0:
                        self.logger.warning(f"字段 {field} 有 {null_count} 个空值")

            self.logger.info("数据验证完成")
            return True

        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False

    def get_date_range(self) -> Tuple[str, str]:
        """
        获取数据日期范围

        Returns:
            (开始日期, 结束日期) 的元组
        """
        if self.data_cache is None:
            # 如果没有缓存，从文件名推断日期范围
            dates = []
            for file_path in self.file_list:
                try:
                    # 从文件名提取日期 (格式: YYYYMMDD.csv)
                    date_str = file_path.stem
                    date_obj = datetime.strptime(date_str, '%Y%m%d')
                    dates.append(date_obj)
                except ValueError:
                    continue

            if dates:
                dates.sort()
                return (dates[0].strftime('%Y-%m-%d'), dates[-1].strftime('%Y-%m-%d'))
            else:
                return ("未知", "未知")
        else:
            # 从缓存数据获取日期范围
            if isinstance(self.data_cache.index, pd.MultiIndex) and 'date' in self.data_cache.index.names:
                dates = self.data_cache.index.get_level_values('date')
                return (dates.min().strftime('%Y-%m-%d'), dates.max().strftime('%Y-%m-%d'))
            else:
                return ("未知", "未知")

    def get_sector_list(self) -> List[str]:
        """
        获取所有板块列表

        Returns:
            板块代码列表
        """
        if self.data_cache is None:
            self.load_all_data()

        if isinstance(self.data_cache.index, pd.MultiIndex) and 'sector_code' in self.data_cache.index.names:
            return sorted(self.data_cache.index.get_level_values('sector_code').unique().tolist())
        else:
            return []

    def get_data_summary(self) -> Dict:
        """
        获取数据摘要信息

        Returns:
            包含数据摘要的字典
        """
        if self.data_cache is None:
            self.load_all_data()

        date_range = self.get_date_range()
        sector_list = self.get_sector_list()

        summary = {
            'total_records': len(self.data_cache),
            'date_range': date_range,
            'total_sectors': len(sector_list),
            'total_files': len(self.file_list),
            'memory_usage_mb': self.data_cache.memory_usage(deep=True).sum() / 1024 / 1024,
            'columns': list(self.data_cache.columns)
        }

        return summary

    def clear_cache(self):
        """清除缓存数据"""
        self.data_cache = None
        self.logger.info("数据缓存已清除")
