#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试历史-未来板块一致性分析功能修复
验证数据统计错误是否已修复
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from data_loader import DataLoader
from time_window_analyzer import TimeWindowAnalyzer
from ranking_analyzer import RankingAnalyzer
from historical_analyzer import HistoricalAnalyzer
from report_generator import ReportGenerator
import config

def test_consistency_analysis_fix():
    """测试一致性分析修复"""
    print("🔧 测试历史-未来板块一致性分析功能修复")
    print("=" * 60)
    
    try:
        # 1. 加载数据
        print("📊 加载数据...")
        data_loader = DataLoader()
        data = data_loader.load_all_data()
        
        if data.empty:
            print("❌ 数据加载失败")
            return False
            
        print(f"✅ 数据加载成功，共 {len(data)} 条记录")
        
        # 2. 初始化分析器
        analyzer = TimeWindowAnalyzer(data)
        ranking_analyzer = RankingAnalyzer(data)
        historical_analyzer = HistoricalAnalyzer(data)
        
        # 使用最新日期作为分析日期
        end_date = analyzer.available_dates[-1]
        print(f"📅 分析日期: {end_date}")
        
        # 3. 执行历史时间窗口分析
        print("\n📈 执行历史时间窗口分析...")
        window_results = {}
        for window_days in config.TIME_WINDOWS:
            performance = analyzer.calculate_window_performance(end_date, window_days)
            if not performance.empty:
                window_results[window_days] = performance
                print(f"  历史{window_days}日窗口: {len(performance)} 个板块")
        
        # 4. 执行未来时间窗口分析
        print("\n🔮 执行未来时间窗口分析...")
        future_window_results = analyzer.calculate_multiple_future_windows(end_date, config.FUTURE_TIME_WINDOWS)
        future_return_rankings = analyzer.calculate_multiple_future_return_rankings(end_date, config.FUTURE_TIME_WINDOWS, top_n=10)
        
        for window_days in config.FUTURE_TIME_WINDOWS:
            if window_days in future_window_results and not future_window_results[window_days].empty:
                print(f"  未来{window_days}日窗口: {len(future_window_results[window_days])} 个板块")
        
        # 5. 执行排名统计分析
        print("\n📊 执行排名统计分析...")
        multiple_window_rankings = ranking_analyzer.calculate_multiple_window_rankings(config.TIME_WINDOWS)
        
        # 6. 执行历史同期分析
        print("\n📈 执行历史同期分析...")
        historical_analysis = historical_analyzer.calculate_historical_windows(end_date, config.TIME_WINDOWS)
        
        # 7. 测试一致性分析
        print("\n🔄 测试一致性分析修复...")
        report_generator = ReportGenerator("output")
        
        # 准备测试数据
        test_data = {
            'window_performance': window_results,
            'future_window_performance': future_window_results,
            'future_return_rankings': future_return_rankings,
            'multiple_window_rankings': multiple_window_rankings,
            'historical_analysis': historical_analysis,
            'future_analysis_enabled': True
        }
        
        # 调用修复后的一致性分析方法
        consistency_results = report_generator._calculate_consistency_overlap(
            window_results, 
            multiple_window_rankings, 
            future_window_results, 
            future_return_rankings, 
            historical_analysis
        )
        
        # 8. 验证修复结果
        print("\n✅ 验证修复结果:")
        print("-" * 40)
        
        if not consistency_results:
            print("❌ 一致性分析结果为空")
            return False
            
        overlap_sectors = consistency_results.get('overlap_sectors', [])
        print(f"📊 重合板块总数: {len(overlap_sectors)}")
        print(f"📊 历史优秀板块总数: {consistency_results.get('total_historical_sectors', 0)}")
        print(f"📊 未来预测板块总数: {consistency_results.get('total_future_sectors', 0)}")
        
        # 查找煤炭行业数据
        coal_sector = None
        for sector in overlap_sectors:
            if '煤炭' in sector['sector_name']:
                coal_sector = sector
                break
        
        if coal_sector:
            print(f"\n🔍 煤炭行业详细信息验证:")
            print(f"  板块名称: {coal_sector['sector_name']}")
            print(f"  历史得分: {coal_sector['historical_score']}")
            print(f"  未来得分: {coal_sector['future_score']}")
            print(f"  重合度评分: {coal_sector['overlap_score']:.2f}")
            print(f"  历史时间窗口数: {coal_sector['historical_windows']}")
            print(f"  未来时间窗口数: {coal_sector['future_windows']}")
            
            print(f"\n  历史出现情况 ({len(coal_sector['historical_appearances'])}项):")
            for i, appearance in enumerate(coal_sector['historical_appearances'][:5]):
                print(f"    {i+1}. {appearance['type']}: {appearance['window_days']}日第{appearance['rank']}名")
            
            print(f"\n  未来出现情况 ({len(coal_sector['future_appearances'])}项):")
            for i, appearance in enumerate(coal_sector['future_appearances']):
                print(f"    {i+1}. {appearance['type']}: {appearance['window_days']}日第{appearance['rank']}名")
        else:
            print("\n⚠️ 未找到煤炭行业数据")
        
        # 9. 生成详细信息测试
        print(f"\n📝 测试详细信息生成:")
        if coal_sector:
            detail_info = report_generator._generate_sector_detail_info(coal_sector)
            print(f"  详细信息: {detail_info}")
        
        print(f"\n✅ 一致性分析功能修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_consistency_analysis_fix()
    if success:
        print("\n🎉 测试通过！一致性分析功能修复成功")
    else:
        print("\n❌ 测试失败！需要进一步检查")
