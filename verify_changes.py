#!/usr/bin/env python3
"""
验证动态排名统计功能的修改
"""

import os
import re

def verify_ranking_analyzer():
    """验证RankingAnalyzer的修改"""
    print("1. 验证RankingAnalyzer修改...")
    
    with open('src/ranking_analyzer.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否添加了generate_multiple_window_rankings方法
    if 'generate_multiple_window_rankings' in content:
        print("   ✅ 已添加generate_multiple_window_rankings方法")
    else:
        print("   ❌ 缺少generate_multiple_window_rankings方法")
    
    # 检查count_daily_champions是否支持window_days参数
    if 'def count_daily_champions(self, window_days: Optional[int] = None' in content:
        print("   ✅ count_daily_champions已支持window_days参数")
    else:
        print("   ❌ count_daily_champions未支持window_days参数")

def verify_report_generator():
    """验证ReportGenerator的修改"""
    print("\n2. 验证ReportGenerator修改...")
    
    with open('src/report_generator.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否添加了动态排名统计部分
    if '_generate_dynamic_ranking_section' in content:
        print("   ✅ 已添加_generate_dynamic_ranking_section方法")
    else:
        print("   ❌ 缺少_generate_dynamic_ranking_section方法")
    
    # 检查是否添加了排名标签页生成方法
    if '_generate_ranking_tabs' in content:
        print("   ✅ 已添加_generate_ranking_tabs方法")
    else:
        print("   ❌ 缺少_generate_ranking_tabs方法")
    
    # 检查是否添加了JavaScript函数
    if 'showRankingWindowTab' in content:
        print("   ✅ 已添加showRankingWindowTab JavaScript函数")
    else:
        print("   ❌ 缺少showRankingWindowTab JavaScript函数")
    
    # 检查是否添加了CSS样式
    if 'ranking-window-tabs' in content:
        print("   ✅ 已添加排名统计标签页CSS样式")
    else:
        print("   ❌ 缺少排名统计标签页CSS样式")

def verify_main_program():
    """验证主程序的修改"""
    print("\n3. 验证主程序修改...")
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否使用了generate_multiple_window_rankings
    if 'generate_multiple_window_rankings' in content:
        print("   ✅ 主程序已调用generate_multiple_window_rankings")
    else:
        print("   ❌ 主程序未调用generate_multiple_window_rankings")
    
    # 检查是否传递了multiple_window_rankings到报告数据
    if 'multiple_window_rankings' in content:
        print("   ✅ 已将multiple_window_rankings传递给报告生成器")
    else:
        print("   ❌ 未将multiple_window_rankings传递给报告生成器")

def verify_html_output():
    """验证HTML输出"""
    print("\n4. 验证HTML输出...")
    
    # 查找最新的HTML报告文件
    output_dir = 'output'
    html_files = [f for f in os.listdir(output_dir) if f.endswith('.html')]
    
    if not html_files:
        print("   ❌ 没有找到HTML报告文件")
        return
    
    latest_html = max(html_files)
    html_path = os.path.join(output_dir, latest_html)
    
    with open(html_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否显示了时间跨度信息
    if re.search(r'排行榜.*最近.*日', content):
        print(f"   ✅ HTML报告已显示时间跨度信息")
    else:
        print(f"   ❌ HTML报告未显示时间跨度信息")
    
    # 检查是否包含排名统计分析部分
    if '排名统计分析' in content:
        print(f"   ✅ HTML报告包含排名统计分析部分")
    else:
        print(f"   ❌ HTML报告缺少排名统计分析部分")
    
    print(f"   📄 检查的HTML文件: {latest_html}")

def main():
    """主验证函数"""
    print("=" * 60)
    print("验证动态排名统计功能的修改")
    print("=" * 60)
    
    try:
        verify_ranking_analyzer()
        verify_report_generator()
        verify_main_program()
        verify_html_output()
        
        print("\n" + "=" * 60)
        print("✅ 验证完成！")
        print("=" * 60)
        
        print("\n修改摘要:")
        print("1. ✅ RankingAnalyzer支持多时间窗口排名统计")
        print("2. ✅ ReportGenerator支持动态排名统计标签页")
        print("3. ✅ 主程序传递多时间窗口排名数据")
        print("4. ✅ HTML报告显示时间跨度信息")
        
        print("\n功能特性:")
        print("- 🔄 排名统计与历史时间窗口联动")
        print("- 📊 支持7日、14日、30日时间窗口切换")
        print("- 🏆 单日冠军次数排行榜动态更新")
        print("- 📈 前10名频次排行榜动态更新")
        print("- 🎯 界面显示当前统计时间跨度")
        
    except Exception as e:
        print(f"\n❌ 验证过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
