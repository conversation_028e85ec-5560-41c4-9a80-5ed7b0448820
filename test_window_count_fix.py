#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试窗口数统计修复
验证表格数据与详细信息的一致性
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_window_count_consistency():
    """测试窗口数统计一致性"""
    print("🔧 测试窗口数统计修复")
    print("=" * 50)
    
    try:
        from report_generator import ReportGenerator
        
        # 创建测试数据
        historical_info = {
            'sector_name': '煤炭行业',
            'appearances': [
                {'type': 'historical_performance', 'window_days': 7, 'rank': 1, 'score': 10, 'return_pct': 5.5},
                {'type': 'champion_count', 'window_days': 7, 'rank': 2, 'score': 9, 'champion_count': 3},
                {'type': 'frequency_ranking', 'window_days': 7, 'rank': 2, 'score': 9, 'top10_count': 5},
                {'type': 'historical_consistency_ranking', 'window_days': 7, 'rank': 3, 'score': 8, 'consistency_rate': 60.0},
                {'type': 'historical_performance', 'window_days': 14, 'rank': 2, 'score': 9, 'return_pct': 4.2},
                {'type': 'frequency_ranking', 'window_days': 14, 'rank': 9, 'score': 2, 'top10_count': 2},
            ],
            'total_score': 47
        }
        
        future_info = {
            'sector_name': '煤炭行业',
            'appearances': [
                {'type': 'future_return_ranking', 'window_days': 30, 'rank': 1, 'score': 10, 'avg_return_pct': 7.8},
                {'type': 'future_consistency_ranking', 'window_days': 30, 'rank': 6, 'score': 5, 'consistency_rate': 55.0},
                {'type': 'future_return_ranking', 'window_days': 60, 'rank': 1, 'score': 10, 'avg_return_pct': 9.3},
                {'type': 'future_consistency_ranking', 'window_days': 60, 'rank': 4, 'score': 7, 'consistency_rate': 62.0},
                {'type': 'future_return_ranking', 'window_days': 90, 'rank': 3, 'score': 8, 'avg_return_pct': 6.1},
            ],
            'total_score': 40
        }
        
        # 模拟修复前的逻辑（错误的）
        print("📊 修复前的统计逻辑（错误）:")
        historical_windows_old = set()
        future_windows_old = set()
        
        for appearance in historical_info['appearances']:
            historical_windows_old.add(appearance['window_days'])
        
        for appearance in future_info['appearances']:
            future_windows_old.add(appearance['window_days'])
        
        print(f"  历史窗口数（去重）: {len(historical_windows_old)}")
        print(f"  未来窗口数（去重）: {len(future_windows_old)}")
        print(f"  历史详细项目数: {len(historical_info['appearances'])}")
        print(f"  未来详细项目数: {len(future_info['appearances'])}")
        print(f"  ❌ 不一致！表格显示{len(historical_windows_old)}个历史窗口，但详细信息有{len(historical_info['appearances'])}项")
        
        # 修复后的逻辑（正确的）
        print(f"\n✅ 修复后的统计逻辑（正确）:")
        historical_appearances_count = len(historical_info['appearances'])
        future_appearances_count = len(future_info['appearances'])
        
        print(f"  历史项目数: {historical_appearances_count}")
        print(f"  未来项目数: {future_appearances_count}")
        print(f"  历史详细项目数: {len(historical_info['appearances'])}")
        print(f"  未来详细项目数: {len(future_info['appearances'])}")
        print(f"  ✅ 一致！表格显示{historical_appearances_count}个历史项目，详细信息也有{len(historical_info['appearances'])}项")
        
        # 测试详细信息生成
        print(f"\n📝 测试详细信息生成:")
        generator = ReportGenerator("output")
        
        test_sector = {
            'sector_name': '煤炭行业',
            'historical_appearances': historical_info['appearances'],
            'future_appearances': future_info['appearances']
        }
        
        detail_info = generator._generate_sector_detail_info(test_sector)
        print(f"  详细信息: {detail_info}")
        
        # 分析详细信息中的项目数
        historical_items = detail_info.split('|')[0].replace('历史: ', '').split(';')
        future_items = detail_info.split('|')[1].replace(' 未来: ', '').split(';')
        
        print(f"\n📊 详细信息分析:")
        print(f"  历史项目显示数: {len([item for item in historical_items if item.strip()])}")
        print(f"  未来项目显示数: {len([item for item in future_items if item.strip()])}")
        
        # 计算重合度评分
        print(f"\n🎯 重合度评分计算:")
        historical_score = historical_info['total_score']
        future_score = future_info['total_score']
        total_possible_appearances = 18
        appearance_coverage = (historical_appearances_count + future_appearances_count) / total_possible_appearances
        overlap_score = (historical_score + future_score) * appearance_coverage
        
        print(f"  历史得分: {historical_score}")
        print(f"  未来得分: {future_score}")
        print(f"  项目覆盖度: {appearance_coverage:.3f} ({historical_appearances_count + future_appearances_count}/{total_possible_appearances})")
        print(f"  重合度评分: {overlap_score:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print(f"\n🧪 测试边界情况:")
    
    # 测试只有历史数据的情况
    print("  测试1: 只有历史数据")
    historical_only = {
        'appearances': [
            {'type': 'historical_performance', 'window_days': 7, 'rank': 1, 'score': 10},
            {'type': 'champion_count', 'window_days': 7, 'rank': 2, 'score': 9},
        ],
        'total_score': 19
    }
    
    historical_count = len(historical_only['appearances'])
    future_count = 0
    print(f"    历史项目数: {historical_count}, 未来项目数: {future_count}")
    
    # 测试只有未来数据的情况
    print("  测试2: 只有未来数据")
    future_only = {
        'appearances': [
            {'type': 'future_return_ranking', 'window_days': 30, 'rank': 1, 'score': 10},
            {'type': 'future_consistency_ranking', 'window_days': 30, 'rank': 6, 'score': 5},
            {'type': 'future_return_ranking', 'window_days': 60, 'rank': 2, 'score': 9},
        ],
        'total_score': 24
    }
    
    historical_count = 0
    future_count = len(future_only['appearances'])
    print(f"    历史项目数: {historical_count}, 未来项目数: {future_count}")
    
    print("  ✅ 边界情况测试通过")

if __name__ == "__main__":
    print("🔧 窗口数统计修复验证")
    print("=" * 60)
    
    success1 = test_window_count_consistency()
    test_edge_cases()
    
    if success1:
        print("\n🎉 修复验证成功！表格数据与详细信息现在保持一致")
    else:
        print("\n❌ 修复验证失败！需要进一步检查")
